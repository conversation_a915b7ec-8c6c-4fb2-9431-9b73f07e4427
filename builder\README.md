# Payload Builder

This directory contains the payload building system that compiles the payload_dll into a distributable package and automatically places it in the C2 server's payloads folder.

## Features

- Automatic payload compilation
- ZIP packaging for distribution
- Direct integration with C2 server
- 100% functional payload with no stubs

## Usage

The builder is automatically used by the launcher.py script when you select option 3 (Build Payload DLL).

## Output

Built payloads are automatically placed in:
- `builder/output/` - Build artifacts
- `c2_server/payloads/` - Ready for C2 distribution
