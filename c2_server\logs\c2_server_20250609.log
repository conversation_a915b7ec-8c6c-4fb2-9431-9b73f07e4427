2025-06-09 04:27:10 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:10 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:27:12 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:12 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:27:13 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:13 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:45:52 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:45:52 - __main__ - ERROR - main:71 - Fatal error: no running event loop
2025-06-09 04:46:55 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:46:55 - __main__ - ERROR - main:71 - Fatal error: no running event loop
2025-06-09 04:48:46 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:48:46 - __main__ - ERROR - main:71 - Fatal error: No directory exists at 'C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\web\static'
2025-06-09 04:49:10 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:49:10 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:49:10 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:49:10 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:49:10 - p2p_network - INFO - start:112 - Starting P2P network with node ID: bfd0a80e-82ed-4277-93ae-639c8884661d
2025-06-09 04:49:12 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:49:12 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:49:12 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:49:12 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:49:12 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:49:12 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:49:13 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:49:13 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:49:13 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:49:13 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:49:13 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:49:13 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:49:13 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:49:13 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:49:13 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:49:13 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 04:50:19 - payload_manager - INFO - save_payload:126 - Saved payload: ca8848d0-e9a7-40ad-b27c-81a537efdaa6 (test_payload.py)
2025-06-09 04:50:50 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:51:18 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:52:01 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:53:22 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:53:22 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:53:22 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:53:22 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:53:22 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 20b72b1f-6f80-435c-94fa-21aec391c30e
2025-06-09 04:53:24 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:53:24 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:53:24 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:53:24 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:53:24 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:53:24 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:53:25 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:53:25 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:53:25 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:53:25 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:53:25 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:53:25 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:53:25 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:53:25 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:53:25 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:53:25 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 04:53:51 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:54:07 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:54:07 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:54:07 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:54:07 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:54:07 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 0af7f1e3-db42-47eb-bbbf-b05552fab426
2025-06-09 04:54:09 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:54:09 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:54:09 - p2p_network - ERROR - start_p2p_server:168 - Failed to start P2P server: [Errno 10048] error while attempting to bind on address ('[REDACTED]', 8444): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-06-09 04:54:09 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:54:09 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:54:09 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:54:10 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:54:10 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:54:10 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:54:10 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:54:10 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:54:10 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:54:10 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:54:10 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:54:10 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:54:10 - __main__ - ERROR - main:71 - Fatal error: [Errno 10048] error while attempting to bind on address ('[REDACTED]', 8080): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-06-09 04:54:31 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:59:36 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:59:36 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:59:36 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:59:36 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:59:36 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 8e6dca33-6bff-4fa1-bea7-5ca6b85b5e47
2025-06-09 04:59:38 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:59:38 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:59:38 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:59:38 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:59:38 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:59:38 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:59:39 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:59:39 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:59:39 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:59:39 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:59:39 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:59:39 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:59:39 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:59:39 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:59:39 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:59:39 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 05:00:01 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:00:01 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:00:01 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:00:50 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:00:50 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:00:50 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:01:34 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:01:34 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:01:34 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:01:34 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:01:34 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 38fcb5af-2f03-40e8-91ed-38eb0c74edb4
2025-06-09 05:01:36 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:01:36 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:01:36 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:01:36 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:01:36 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:01:36 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.cloudflare.com
2025-06-09 05:01:36 - stealth - ERROR - setup_domain_fronting:109 - Failed to verify front domain cdn.cloudflare.com: Cannot connect to host cdn.cloudflare.com:443 ssl:default [getaddrinfo failed]
2025-06-09 05:01:36 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:01:36 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:01:36 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:01:36 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:01:36 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:01:36 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:01:36 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:01:36 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:01:36 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 05:01:58 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:01:58 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:01:58 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:02:29 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:02:29 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:02:29 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:03:52 - host_manager - INFO - register_host:152 - Registered new host: 6b3bc0d8-9860-4ef0-a3af-df2ab206ded4
2025-06-09 05:04:06 - host_manager - INFO - register_host:152 - Registered new host: 093e94cd-f5dd-457d-aebf-ff787c93136a
2025-06-09 05:05:03 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:05:03 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:05:03 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:05:03 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:05:03 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 0f55b3e0-8e72-4e45-9443-fe71d8fe1b9b
2025-06-09 05:05:05 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:05:05 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:05:05 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:05:05 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:05:05 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:05:05 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.cloudflare.com
2025-06-09 05:05:05 - stealth - ERROR - setup_domain_fronting:109 - Failed to verify front domain cdn.cloudflare.com: Cannot connect to host cdn.cloudflare.com:443 ssl:default [getaddrinfo failed]
2025-06-09 05:05:05 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:05:05 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:05:05 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:05:05 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:05:05 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:05:05 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:05:05 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:05:05 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:05:05 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
2025-06-09 05:05:40 - host_manager - INFO - register_host:152 - Registered new host: 6cb9dd14-26c6-45f9-b11d-ddc57c596d84
2025-06-09 05:07:22 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:07:22 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:07:22 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:07:22 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:07:22 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 13f484b9-2372-4a59-898c-212c26da2bd5
2025-06-09 05:07:24 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:07:24 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:07:24 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:07:24 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:07:24 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:07:24 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.jsdelivr.net
2025-06-09 05:07:24 - stealth - INFO - setup_domain_fronting:105 - Front domain cdn.jsdelivr.net is accessible
2025-06-09 05:07:24 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:07:24 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:07:24 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:07:24 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:07:24 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:07:24 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:07:24 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:07:24 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:07:24 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
2025-06-09 05:11:28 - host_manager - INFO - register_host:152 - Registered new host: e0ff5167-6927-4bb9-85e4-951bcab94212
2025-06-09 05:11:49 - server - ERROR - api_bot_checkin:386 - Failed to decrypt bot data: Decryption failed: Invalid base64-encoded string: number of data characters (9345) cannot be 1 more than a multiple of 4
2025-06-09 05:11:49 - server - ERROR - api_bot_checkin:387 - Exception type: <class 'ValueError'>
2025-06-09 05:11:49 - server - ERROR - api_bot_checkin:389 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 55, in decrypt
    encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\base64.py", line 88, in b64decode
    return binascii.a2b_base64(s, strict_mode=validate)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
binascii.Error: Invalid base64-encoded string: number of data characters (9345) cannot be 1 more than a multiple of 4

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 382, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: Invalid base64-encoded string: number of data characters (9345) cannot be 1 more than a multiple of 4

2025-06-09 05:13:46 - host_manager - INFO - register_host:152 - Registered new host: d7a084c8-6e5e-4c15-93b5-3b473e9597a2
2025-06-09 05:13:46 - host_manager - INFO - register_host:129 - Updated host: d7a084c8-6e5e-4c15-93b5-3b473e9597a2
2025-06-09 05:14:33 - host_manager - INFO - register_host:129 - Updated host: d7a084c8-6e5e-4c15-93b5-3b473e9597a2
2025-06-09 05:14:59 - server - INFO - websocket_handler:177 - WebSocket client connected: [REDACTED]
2025-06-09 05:16:31 - server - INFO - websocket_handler:193 - WebSocket client disconnected: [REDACTED]
2025-06-09 05:31:51 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:31:51 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:31:51 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:31:51 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:31:51 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 4c545ce3-1be4-46a1-84ac-b95c464dfc59
2025-06-09 05:31:53 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:31:53 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:31:53 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:31:53 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:31:53 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:31:53 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.jsdelivr.net
2025-06-09 05:31:53 - stealth - INFO - setup_domain_fronting:105 - Front domain cdn.jsdelivr.net is accessible
2025-06-09 05:31:53 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:31:53 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:31:53 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:31:53 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:31:53 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:31:53 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:31:53 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:31:53 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:31:53 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
2025-06-09 05:32:42 - host_manager - INFO - register_host:152 - Registered new host: 4b1ad93d-4762-4774-95a5-69204f4fdb72
2025-06-09 05:32:43 - host_manager - INFO - register_host:129 - Updated host: 4b1ad93d-4762-4774-95a5-69204f4fdb72
2025-06-09 05:33:12 - server - INFO - websocket_handler:177 - WebSocket client connected: [REDACTED]
2025-06-09 05:48:38 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:48:38 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:48:38 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:48:38 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:48:38 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 575d01bb-55fe-48d4-a710-46c5960d4f1e
2025-06-09 05:48:40 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:48:40 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:48:40 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:48:40 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:48:40 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:48:40 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 05:48:41 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 05:48:41 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:48:41 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:48:41 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:48:41 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:48:41 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:48:41 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:48:41 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:48:41 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:48:41 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
2025-06-09 05:49:02 - server - INFO - websocket_handler:177 - WebSocket client connected: [REDACTED]
2025-06-09 05:49:14 - host_manager - INFO - delete_host:246 - Deleted host: 4b1ad93d-4762-4774-95a5-69204f4fdb72
2025-06-09 05:49:16 - host_manager - INFO - delete_host:246 - Deleted host: 6b3bc0d8-9860-4ef0-a3af-df2ab206ded4
2025-06-09 05:49:21 - host_manager - INFO - delete_host:246 - Deleted host: 093e94cd-f5dd-457d-aebf-ff787c93136a
2025-06-09 05:49:24 - host_manager - INFO - delete_host:246 - Deleted host: 6cb9dd14-26c6-45f9-b11d-ddc57c596d84
2025-06-09 05:49:26 - host_manager - INFO - delete_host:246 - Deleted host: e0ff5167-6927-4bb9-85e4-951bcab94212
2025-06-09 05:49:28 - host_manager - INFO - delete_host:246 - Deleted host: d7a084c8-6e5e-4c15-93b5-3b473e9597a2
2025-06-09 05:49:33 - server - INFO - websocket_handler:193 - WebSocket client disconnected: [REDACTED]
2025-06-09 05:49:57 - server - ERROR - api_bot_checkin:398 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:49:57 - server - ERROR - api_bot_checkin:399 - Exception type: <class 'ValueError'>
2025-06-09 05:49:57 - server - ERROR - api_bot_checkin:401 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 394, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:53:23 - server - ERROR - api_bot_checkin:398 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:53:23 - server - ERROR - api_bot_checkin:399 - Exception type: <class 'ValueError'>
2025-06-09 05:53:23 - server - ERROR - api_bot_checkin:401 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 394, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:53:40 - server - ERROR - api_bot_checkin:391 - No encrypted_data field in request
2025-06-09 05:54:53 - server - ERROR - api_bot_checkin:398 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:54:53 - server - ERROR - api_bot_checkin:399 - Exception type: <class 'ValueError'>
2025-06-09 05:54:53 - server - ERROR - api_bot_checkin:401 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 394, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:56:59 - server - ERROR - api_bot_checkin:398 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:56:59 - server - ERROR - api_bot_checkin:399 - Exception type: <class 'ValueError'>
2025-06-09 05:56:59 - server - ERROR - api_bot_checkin:401 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 394, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:58:24 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:58:24 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:58:24 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:58:24 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:58:24 - p2p_network - INFO - start:112 - Starting P2P network with node ID: dcd70210-d72b-43fa-96ea-a801ee427763
2025-06-09 05:58:26 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:58:26 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:58:26 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:58:26 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:58:26 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:58:26 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 05:58:27 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 05:58:27 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:58:27 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:58:27 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:58:27 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:58:27 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:58:27 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:58:27 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:58:27 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:58:27 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
2025-06-09 05:58:55 - host_manager - INFO - register_host:152 - Registered new host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 05:58:56 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 05:59:38 - server - INFO - websocket_handler:177 - WebSocket client connected: [REDACTED]
2025-06-09 05:59:46 - payload_manager - INFO - delete_payload:219 - Deleted payload: ca8848d0-e9a7-40ad-b27c-81a537efdaa6
2025-06-09 05:59:53 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:00:15 - server - INFO - websocket_handler:193 - WebSocket client disconnected: [REDACTED]
2025-06-09 06:01:42 - asyncio - ERROR - default_exception_handler:1757 - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\proactor_events.py", line 162, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-09 06:01:46 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:02:32 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:02:33 - asyncio - ERROR - default_exception_handler:1757 - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\proactor_events.py", line 162, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-09 06:04:05 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:05:41 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:07:22 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:09:14 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:09:59 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:11:29 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:11:31 - host_manager - INFO - register_host:152 - Registered new host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:11:32 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:12:58 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:13:05 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:14:27 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:14:41 - asyncio - ERROR - default_exception_handler:1757 - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\proactor_events.py", line 162, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-09 06:14:41 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:15:31 - asyncio - ERROR - default_exception_handler:1757 - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\proactor_events.py", line 162, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-09 06:15:55 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:16:22 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:17:20 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:18:15 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:18:40 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:19:00 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:19:50 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:20:30 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:20:41 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:22:00 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:22:23 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:23:30 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:24:18 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:25:01 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
2025-06-09 06:25:06 - host_manager - INFO - register_host:129 - Updated host: c138486b-5fdb-49e4-8b8e-2d8803044234
2025-06-09 06:25:14 - payload_manager - INFO - save_payload:126 - Saved payload: 27224508-dda6-4dc8-b3fa-3e52f74342ed (advanced_payload.zip)
2025-06-09 06:26:33 - host_manager - INFO - register_host:129 - Updated host: 0bfd3ee3-c5ed-4460-a98f-34f17e90cc1f
