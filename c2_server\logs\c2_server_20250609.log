2025-06-09 04:27:10 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:10 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:27:12 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:12 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:27:13 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:13 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:45:52 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:45:52 - __main__ - ERROR - main:71 - Fatal error: no running event loop
2025-06-09 04:46:55 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:46:55 - __main__ - ERROR - main:71 - Fatal error: no running event loop
2025-06-09 04:48:46 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:48:46 - __main__ - ERROR - main:71 - Fatal error: No directory exists at 'C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\web\static'
2025-06-09 04:49:10 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:49:10 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:49:10 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:49:10 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:49:10 - p2p_network - INFO - start:112 - Starting P2P network with node ID: bfd0a80e-82ed-4277-93ae-639c8884661d
2025-06-09 04:49:12 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:49:12 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:49:12 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:49:12 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:49:12 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:49:12 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:49:13 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:49:13 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:49:13 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:49:13 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:49:13 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:49:13 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:49:13 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:49:13 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:49:13 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:49:13 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 04:50:19 - payload_manager - INFO - save_payload:126 - Saved payload: ca8848d0-e9a7-40ad-b27c-81a537efdaa6 (test_payload.py)
2025-06-09 04:50:50 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:51:18 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:52:01 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:53:22 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:53:22 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:53:22 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:53:22 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:53:22 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 20b72b1f-6f80-435c-94fa-21aec391c30e
2025-06-09 04:53:24 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:53:24 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:53:24 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:53:24 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:53:24 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:53:24 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:53:25 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:53:25 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:53:25 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:53:25 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:53:25 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:53:25 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:53:25 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:53:25 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:53:25 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:53:25 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 04:53:51 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:54:07 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:54:07 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:54:07 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:54:07 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:54:07 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 0af7f1e3-db42-47eb-bbbf-b05552fab426
2025-06-09 04:54:09 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:54:09 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:54:09 - p2p_network - ERROR - start_p2p_server:168 - Failed to start P2P server: [Errno 10048] error while attempting to bind on address ('[REDACTED]', 8444): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-06-09 04:54:09 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:54:09 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:54:09 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:54:10 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:54:10 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:54:10 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:54:10 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:54:10 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:54:10 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:54:10 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:54:10 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:54:10 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:54:10 - __main__ - ERROR - main:71 - Fatal error: [Errno 10048] error while attempting to bind on address ('[REDACTED]', 8080): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-06-09 04:54:31 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
