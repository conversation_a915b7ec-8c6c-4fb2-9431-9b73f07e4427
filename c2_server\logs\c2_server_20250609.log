2025-06-09 04:27:10 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:10 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:27:12 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:12 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:27:13 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:27:13 - main - ERROR - main:76 - Fatal error: no running event loop
2025-06-09 04:45:52 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:45:52 - __main__ - ERROR - main:71 - Fatal error: no running event loop
2025-06-09 04:46:55 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:46:55 - __main__ - ERROR - main:71 - Fatal error: no running event loop
2025-06-09 04:48:46 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:48:46 - __main__ - ERROR - main:71 - Fatal error: No directory exists at 'C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\web\static'
2025-06-09 04:49:10 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:49:10 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:49:10 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:49:10 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:49:10 - p2p_network - INFO - start:112 - Starting P2P network with node ID: bfd0a80e-82ed-4277-93ae-639c8884661d
2025-06-09 04:49:12 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:49:12 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:49:12 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:49:12 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:49:12 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:49:12 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:49:13 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:49:13 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:49:13 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:49:13 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:49:13 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:49:13 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:49:13 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:49:13 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:49:13 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:49:13 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 04:50:19 - payload_manager - INFO - save_payload:126 - Saved payload: ca8848d0-e9a7-40ad-b27c-81a537efdaa6 (test_payload.py)
2025-06-09 04:50:50 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:51:18 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:52:01 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:53:22 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:53:22 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:53:22 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:53:22 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:53:22 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 20b72b1f-6f80-435c-94fa-21aec391c30e
2025-06-09 04:53:24 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:53:24 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:53:24 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:53:24 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:53:24 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:53:24 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:53:25 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:53:25 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:53:25 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:53:25 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:53:25 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:53:25 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:53:25 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:53:25 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:53:25 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:53:25 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 04:53:51 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:54:07 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:54:07 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:54:07 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:54:07 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:54:07 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 0af7f1e3-db42-47eb-bbbf-b05552fab426
2025-06-09 04:54:09 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:54:09 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:54:09 - p2p_network - ERROR - start_p2p_server:168 - Failed to start P2P server: [Errno 10048] error while attempting to bind on address ('[REDACTED]', 8444): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-06-09 04:54:09 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:54:09 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:54:09 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:54:10 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:54:10 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:54:10 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:54:10 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:54:10 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:54:10 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:54:10 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:54:10 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:54:10 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:54:10 - __main__ - ERROR - main:71 - Fatal error: [Errno 10048] error while attempting to bind on address ('[REDACTED]', 8080): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-06-09 04:54:31 - server - ERROR - api_bot_checkin:367 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 04:59:36 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 04:59:36 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 04:59:36 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 04:59:36 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 04:59:36 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 8e6dca33-6bff-4fa1-bea7-5ca6b85b5e47
2025-06-09 04:59:38 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 04:59:38 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 04:59:38 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 04:59:38 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 04:59:38 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 04:59:38 - stealth - INFO - setup_domain_fronting:98 - Using front domain: ajax.googleapis.com
2025-06-09 04:59:39 - stealth - INFO - setup_domain_fronting:105 - Front domain ajax.googleapis.com is accessible
2025-06-09 04:59:39 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 04:59:39 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 04:59:39 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 04:59:39 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 04:59:39 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 04:59:39 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 04:59:39 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 04:59:39 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 04:59:39 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 05:00:01 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:00:01 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:00:01 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:00:50 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:00:50 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:00:50 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:01:34 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:01:34 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:01:34 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:01:34 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:01:34 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 38fcb5af-2f03-40e8-91ed-38eb0c74edb4
2025-06-09 05:01:36 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:01:36 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:01:36 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:01:36 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:01:36 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:01:36 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.cloudflare.com
2025-06-09 05:01:36 - stealth - ERROR - setup_domain_fronting:109 - Failed to verify front domain cdn.cloudflare.com: Cannot connect to host cdn.cloudflare.com:443 ssl:default [getaddrinfo failed]
2025-06-09 05:01:36 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:01:36 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:01:36 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:01:36 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:01:36 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:01:36 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:01:36 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:01:36 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:01:36 - server - INFO - start:140 - C2 Server started on [REDACTED]:8080
2025-06-09 05:01:58 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:01:58 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:01:58 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:02:29 - server - ERROR - api_bot_checkin:376 - Failed to decrypt bot data: Decryption failed: 
2025-06-09 05:02:29 - server - ERROR - api_bot_checkin:377 - Exception type: <class 'ValueError'>
2025-06-09 05:02:29 - server - ERROR - api_bot_checkin:379 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 134, in _verify_signature
    h.verify(data[-32:])
cryptography.exceptions.InvalidSignature: Signature did not match digest.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 56, in decrypt
    decrypted = self.fernet.decrypt(encrypted_bytes)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 91, in decrypt
    return self._decrypt_data(data, timestamp, time_info)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 152, in _decrypt_data
    self._verify_signature(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\cryptography\fernet.py", line 136, in _verify_signature
    raise InvalidToken
cryptography.fernet.InvalidToken

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\core\server.py", line 372, in api_bot_checkin
    decrypted_data = self.crypto_manager.decrypt(encrypted_data)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\worm\c2_server\utils\crypto.py", line 59, in decrypt
    raise ValueError(f"Decryption failed: {e}")
ValueError: Decryption failed: 

2025-06-09 05:03:52 - host_manager - INFO - register_host:152 - Registered new host: 6b3bc0d8-9860-4ef0-a3af-df2ab206ded4
2025-06-09 05:04:06 - host_manager - INFO - register_host:152 - Registered new host: 093e94cd-f5dd-457d-aebf-ff787c93136a
2025-06-09 05:05:03 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:05:03 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:05:03 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:05:03 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:05:03 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 0f55b3e0-8e72-4e45-9443-fe71d8fe1b9b
2025-06-09 05:05:05 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:05:05 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:05:05 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:05:05 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:05:05 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:05:05 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.cloudflare.com
2025-06-09 05:05:05 - stealth - ERROR - setup_domain_fronting:109 - Failed to verify front domain cdn.cloudflare.com: Cannot connect to host cdn.cloudflare.com:443 ssl:default [getaddrinfo failed]
2025-06-09 05:05:05 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:05:05 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:05:05 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:05:05 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:05:05 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:05:05 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:05:05 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:05:05 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:05:05 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
2025-06-09 05:05:40 - host_manager - INFO - register_host:152 - Registered new host: 6cb9dd14-26c6-45f9-b11d-ddc57c596d84
2025-06-09 05:07:22 - root - INFO - setup_logging:107 - Logging system initialized
2025-06-09 05:07:22 - __main__ - INFO - main:58 - Starting C2 Server...
2025-06-09 05:07:22 - host_manager - INFO - init_database:91 - Database initialized successfully
2025-06-09 05:07:22 - payload_manager - INFO - init_database:81 - Payload database initialized successfully
2025-06-09 05:07:22 - p2p_network - INFO - start:112 - Starting P2P network with node ID: 13f484b9-2372-4a59-898c-212c26da2bd5
2025-06-09 05:07:24 - tor_proxy - ERROR - _test_tor_connection:98 - Tor connection test failed: Error connecting to SOCKS5 proxy [REDACTED]:9050: [WinError 10061] No connection could be made because the target machine actively refused it
2025-06-09 05:07:24 - tor_proxy - ERROR - connect:67 - Failed to connect to Tor proxy
2025-06-09 05:07:24 - p2p_network - INFO - start_p2p_server:165 - P2P server listening on [REDACTED]:8444
2025-06-09 05:07:24 - p2p_network - INFO - start:131 - P2P network started successfully
2025-06-09 05:07:24 - stealth - INFO - start:59 - Starting stealth manager
2025-06-09 05:07:24 - stealth - INFO - setup_domain_fronting:98 - Using front domain: cdn.jsdelivr.net
2025-06-09 05:07:24 - stealth - INFO - setup_domain_fronting:105 - Front domain cdn.jsdelivr.net is accessible
2025-06-09 05:07:24 - stealth - INFO - start_traffic_obfuscation:113 - Starting traffic obfuscation
2025-06-09 05:07:24 - stealth - INFO - start_decoy_traffic:126 - Starting decoy traffic generation
2025-06-09 05:07:24 - stealth - INFO - start_anti_forensics:132 - Starting anti-forensics measures
2025-06-09 05:07:24 - stealth - INFO - setup_memory_protection:259 - Setting up memory protection
2025-06-09 05:07:24 - stealth - INFO - setup_log_obfuscation:274 - Setting up log obfuscation
2025-06-09 05:07:24 - stealth - INFO - setup_artifact_cleanup:283 - Setting up artifact cleanup
2025-06-09 05:07:24 - stealth - INFO - start:76 - Stealth manager started successfully
2025-06-09 05:07:24 - stealth - INFO - cleanup_memory_artifacts:292 - Cleaning up memory artifacts
2025-06-09 05:07:24 - server - INFO - start:141 - C2 Server started on [REDACTED]:8080
