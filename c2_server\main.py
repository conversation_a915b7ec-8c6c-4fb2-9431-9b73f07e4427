#!/usr/bin/env python3
"""
Educational C2 Server - Main Entry Point
P2P Command and Control Server with Real-time Dashboard
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.server import C2Server
from core.config import Config
from utils.logger import setup_logging

def main():
    """Main entry point for the C2 server"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Educational C2 Server                     ║
    ║                  P2P Command & Control System               ║
    ║                     For Educational Use Only                ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize configuration
        config = Config()
        
        # Create and start the C2 server
        server = C2Server(config)
        
        logger.info("Starting C2 Server...")
        print(f"[+] C2 Server starting on {config.SERVER_HOST}:{config.SERVER_PORT}")
        print(f"[+] Dashboard available at http://{config.SERVER_HOST}:{config.WEB_PORT}")
        print(f"[+] P2P Network: {config.P2P_ENABLED}")
        print("[+] Press Ctrl+C to stop the server")
        
        # Run the server
        asyncio.run(server.start())
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested by user")
        print("\n[!] Shutting down C2 Server...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"[!] Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
