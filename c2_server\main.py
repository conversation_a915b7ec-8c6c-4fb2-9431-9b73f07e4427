#!/usr/bin/env python3
"""
Educational C2 Server - Main Entry Point
P2P Command and Control Server with Real-time Dashboard
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Direct imports without relative paths
import sys
import os

# Add paths to sys.path for imports
current_dir = str(Path(__file__).parent)
core_dir = os.path.join(current_dir, 'core')
utils_dir = os.path.join(current_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if core_dir not in sys.path:
    sys.path.insert(0, core_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

# Now import modules
from config import Config
from logger import setup_logging
from server import C2Server

def main():
    """Main entry point for the C2 server"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Educational C2 Server                     ║
    ║                  P2P Command & Control System               ║
    ║                     For Educational Use Only                ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize configuration
        config = Config()
        
        # Create and start the C2 server
        server = C2Server(config)
        
        logger.info("Starting C2 Server...")
        print(f"[+] C2 Server starting on {config.SERVER_HOST}:{config.SERVER_PORT}")
        print(f"[+] Dashboard available at http://{config.SERVER_HOST}:{config.WEB_PORT}")
        print(f"[+] P2P Network: {config.P2P_ENABLED}")
        print("[+] Press Ctrl+C to stop the server")
        
        # Run the server
        asyncio.run(server.start())
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested by user")
        print("\n[!] Shutting down C2 Server...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"[!] Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
