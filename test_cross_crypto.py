#!/usr/bin/env python3
"""
Test cross-crypto compatibility
"""

import sys
import os
import json

# Add paths
sys.path.insert(0, 'loader/core')
sys.path.insert(0, 'c2_server/utils')

def test_cross_crypto():
    """Test if loader and server crypto can decrypt each other's data"""
    
    # Import both crypto managers
    from loader.core.crypto import CryptoManager as LoaderCrypto
    from c2_server.utils.crypto import CryptoManager as ServerCrypto
    
    key = "default_key_change_me"
    
    # Create instances
    loader_crypto = LoaderCrypto(key)
    server_crypto = ServerCrypto(key)
    
    # Test data
    test_data = {"hostname": "test", "ip_address": "127.0.0.1"}
    json_data = json.dumps(test_data)
    
    print(f"Original data: {json_data}")
    
    # Test 1: Loader encrypts, Server decrypts
    print("\n=== Test 1: Loader -> Server ===")
    encrypted_by_loader = loader_crypto.encrypt(json_data)
    print(f"Encrypted by loader: {encrypted_by_loader}")
    
    try:
        decrypted_by_server = server_crypto.decrypt(encrypted_by_loader)
        print(f"Decrypted by server: {decrypted_by_server}")
        print(f"Match: {decrypted_by_server == json_data}")
    except Exception as e:
        print(f"Server decrypt failed: {e}")
    
    # Test 2: Server encrypts, Loader decrypts
    print("\n=== Test 2: Server -> Loader ===")
    encrypted_by_server = server_crypto.encrypt(json_data)
    print(f"Encrypted by server: {encrypted_by_server}")
    
    try:
        decrypted_by_loader = loader_crypto.decrypt(encrypted_by_server)
        print(f"Decrypted by loader: {decrypted_by_loader}")
        print(f"Match: {decrypted_by_loader == json_data}")
    except Exception as e:
        print(f"Loader decrypt failed: {e}")

if __name__ == "__main__":
    test_cross_crypto()
