"""
Cryptographic utilities for C2 Server
Provides encryption, decryption, and secure communication functions
"""

import base64
import hashlib
import hmac
import os
import secrets
from typing import Union, Tu<PERSON>, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt

class CryptoManager:
    """Manages cryptographic operations for the C2 server"""
    
    def __init__(self, master_key: str):
        self.master_key = master_key.encode() if isinstance(master_key, str) else master_key
        self.fernet = Fernet(self._derive_fernet_key())
        
        # Generate RSA key pair for asymmetric operations
        self.private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        self.public_key = self.private_key.public_key()
    
    def _derive_fernet_key(self) -> bytes:
        """Derive a Fernet key from the master key"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'loader_salt_key',  # Should match loader
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key))
        return key
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """Encrypt data using Fernet symmetric encryption"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        encrypted = self.fernet.encrypt(data)
        return base64.b64encode(encrypted).decode('utf-8')
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt data using Fernet symmetric encryption"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode('utf-8')
        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")
    
    def encrypt_bytes(self, data: bytes) -> bytes:
        """Encrypt raw bytes"""
        return self.fernet.encrypt(data)
    
    def decrypt_bytes(self, encrypted_data: bytes) -> bytes:
        """Decrypt raw bytes"""
        return self.fernet.decrypt(encrypted_data)
    
    def rsa_encrypt(self, data: Union[str, bytes], public_key=None) -> bytes:
        """Encrypt data using RSA public key"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        key = public_key or self.public_key
        
        encrypted = key.encrypt(
            data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        return encrypted
    
    def rsa_decrypt(self, encrypted_data: bytes) -> bytes:
        """Decrypt data using RSA private key"""
        decrypted = self.private_key.decrypt(
            encrypted_data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        return decrypted
    
    def sign_data(self, data: Union[str, bytes]) -> bytes:
        """Sign data using RSA private key"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        signature = self.private_key.sign(
            data,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return signature
    
    def verify_signature(self, data: Union[str, bytes], signature: bytes, public_key=None) -> bool:
        """Verify signature using RSA public key"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        key = public_key or self.public_key
        
        try:
            key.verify(
                signature,
                data,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False
    
    def generate_session_key(self) -> bytes:
        """Generate a random session key"""
        return secrets.token_bytes(32)
    
    def derive_key(self, password: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """Derive a key from password using Scrypt"""
        if salt is None:
            salt = secrets.token_bytes(16)
        
        kdf = Scrypt(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            n=2**14,
            r=8,
            p=1,
        )
        key = kdf.derive(password.encode())
        return key, salt
    
    def hash_password(self, password: str, salt: bytes = None) -> Tuple[str, str]:
        """Hash password with salt"""
        if salt is None:
            salt = secrets.token_bytes(16)
        
        key, salt = self.derive_key(password, salt)
        return base64.b64encode(key).decode(), base64.b64encode(salt).decode()
    
    def verify_password(self, password: str, hashed: str, salt: str) -> bool:
        """Verify password against hash"""
        try:
            salt_bytes = base64.b64decode(salt.encode())
            key, _ = self.derive_key(password, salt_bytes)
            expected_hash = base64.b64encode(key).decode()
            return hmac.compare_digest(hashed, expected_hash)
        except Exception:
            return False
    
    def generate_hmac(self, data: Union[str, bytes], key: bytes = None) -> str:
        """Generate HMAC for data integrity"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        if key is None:
            key = self.master_key
        
        mac = hmac.new(key, data, hashlib.sha256)
        return base64.b64encode(mac.digest()).decode()
    
    def verify_hmac(self, data: Union[str, bytes], mac: str, key: bytes = None) -> bool:
        """Verify HMAC"""
        expected_mac = self.generate_hmac(data, key)
        return hmac.compare_digest(mac, expected_mac)
    
    def encrypt_file(self, file_path: str, output_path: str = None) -> str:
        """Encrypt a file"""
        if output_path is None:
            output_path = file_path + '.encrypted'
        
        with open(file_path, 'rb') as infile:
            data = infile.read()
        
        encrypted_data = self.encrypt_bytes(data)
        
        with open(output_path, 'wb') as outfile:
            outfile.write(encrypted_data)
        
        return output_path
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str = None) -> str:
        """Decrypt a file"""
        if output_path is None:
            output_path = encrypted_file_path.replace('.encrypted', '')
        
        with open(encrypted_file_path, 'rb') as infile:
            encrypted_data = infile.read()
        
        decrypted_data = self.decrypt_bytes(encrypted_data)
        
        with open(output_path, 'wb') as outfile:
            outfile.write(decrypted_data)
        
        return output_path
    
    def get_public_key_pem(self) -> str:
        """Get public key in PEM format"""
        pem = self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return pem.decode('utf-8')
    
    def get_private_key_pem(self, password: bytes = None) -> str:
        """Get private key in PEM format"""
        encryption_algorithm = serialization.NoEncryption()
        if password:
            encryption_algorithm = serialization.BestAvailableEncryption(password)
        
        pem = self.private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=encryption_algorithm
        )
        return pem.decode('utf-8')
    
    def load_public_key_from_pem(self, pem_data: str):
        """Load public key from PEM data"""
        return serialization.load_pem_public_key(pem_data.encode('utf-8'))
    
    def load_private_key_from_pem(self, pem_data: str, password: bytes = None):
        """Load private key from PEM data"""
        return serialization.load_pem_private_key(
            pem_data.encode('utf-8'),
            password=password
        )

class StreamCipher:
    """Stream cipher for real-time encryption"""
    
    def __init__(self, key: bytes):
        self.key = key
        self.iv = secrets.token_bytes(16)
        self.cipher = Cipher(
            algorithms.AES(key),
            modes.CTR(self.iv)
        )
        self.encryptor = self.cipher.encryptor()
        self.decryptor = self.cipher.decryptor()
    
    def encrypt_chunk(self, data: bytes) -> bytes:
        """Encrypt a chunk of data"""
        return self.encryptor.update(data)
    
    def decrypt_chunk(self, encrypted_data: bytes) -> bytes:
        """Decrypt a chunk of data"""
        return self.decryptor.update(encrypted_data)
    
    def finalize(self):
        """Finalize the cipher"""
        self.encryptor.finalize()
        self.decryptor.finalize()

class SecureRandom:
    """Secure random number generator"""
    
    @staticmethod
    def bytes(length: int) -> bytes:
        """Generate random bytes"""
        return secrets.token_bytes(length)
    
    @staticmethod
    def string(length: int, alphabet: str = None) -> str:
        """Generate random string"""
        if alphabet is None:
            alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def integer(min_val: int, max_val: int) -> int:
        """Generate random integer"""
        return secrets.randbelow(max_val - min_val + 1) + min_val
    
    @staticmethod
    def uuid() -> str:
        """Generate random UUID"""
        import uuid
        return str(uuid.uuid4())

class KeyExchange:
    """Diffie-Hellman key exchange implementation"""
    
    def __init__(self):
        # Using a well-known safe prime
        self.p = int("FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1"
                    "29024E088A67CC74020BBEA63B139B22514A08798E3404DD"
                    "EF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245"
                    "E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7ED"
                    "EE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3D"
                    "C2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F"
                    "83655D23DCA3AD961C62F356208552BB9ED529077096966D"
                    "670C354E4ABC9804F1746C08CA18217C32905E462E36CE3B"
                    "E39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9"
                    "DE2BCBF6955817183995497CEA956AE515D2261898FA0510"
                    "15728E5A8AACAA68FFFFFFFFFFFFFFFF", 16)
        self.g = 2
        self.private_key = secrets.randbelow(self.p - 1) + 1
        self.public_key = pow(self.g, self.private_key, self.p)
    
    def get_public_key(self) -> int:
        """Get public key for exchange"""
        return self.public_key
    
    def compute_shared_secret(self, other_public_key: int) -> bytes:
        """Compute shared secret"""
        shared_secret = pow(other_public_key, self.private_key, self.p)
        # Convert to bytes and hash for use as encryption key
        secret_bytes = shared_secret.to_bytes((shared_secret.bit_length() + 7) // 8, 'big')
        return hashlib.sha256(secret_bytes).digest()

def generate_certificate(hostname: str = "localhost") -> Tuple[str, str]:
    """Generate self-signed certificate for HTTPS"""
    from cryptography import x509
    from cryptography.x509.oid import NameOID
    import datetime
    
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048
    )
    
    # Create certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "CA"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "C2 Server"),
        x509.NameAttribute(NameOID.COMMON_NAME, hostname),
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName(hostname),
        ]),
        critical=False,
    ).sign(private_key, hashes.SHA256())
    
    # Serialize to PEM
    cert_pem = cert.public_bytes(serialization.Encoding.PEM).decode()
    key_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    ).decode()
    
    return cert_pem, key_pem
