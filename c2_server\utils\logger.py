"""
Logging utilities for C2 Server
Provides secure and obfuscated logging capabilities
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

class ObfuscatedFormatter(logging.Formatter):
    """Custom formatter that obfuscates sensitive information"""
    
    def __init__(self, fmt=None, datefmt=None, obfuscate=True):
        super().__init__(fmt, datefmt)
        self.obfuscate = obfuscate
        
        # Sensitive patterns to obfuscate
        self.sensitive_patterns = [
            r'\b(?:\d{1,3}\.){3}\d{1,3}\b',  # IP addresses
            r'\b[A-Fa-f0-9]{32,}\b',         # Hashes/keys
            r'password[=:]\s*\S+',           # Passwords
            r'token[=:]\s*\S+',              # Tokens
            r'key[=:]\s*\S+'                 # Keys
        ]
    
    def format(self, record):
        """Format log record with obfuscation"""
        formatted = super().format(record)
        
        if self.obfuscate:
            import re
            for pattern in self.sensitive_patterns:
                formatted = re.sub(pattern, '[REDACTED]', formatted, flags=re.IGNORECASE)
        
        return formatted

class SecureRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """Secure rotating file handler with encryption support"""
    
    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, 
                 encoding=None, delay=False, encrypt=False, key=None):
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)
        self.encrypt = encrypt
        self.key = key
    
    def emit(self, record):
        """Emit a record with optional encryption"""
        if self.encrypt and self.key:
            # Would implement log encryption here
            pass
        
        super().emit(record)

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None, 
                 obfuscate: bool = True, encrypt_logs: bool = False):
    """Setup logging configuration"""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = ObfuscatedFormatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        obfuscate=obfuscate
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler
    if not log_file:
        log_file = log_dir / f"c2_server_{datetime.now().strftime('%Y%m%d')}.log"
    
    file_handler = SecureRotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encrypt=encrypt_logs
    )
    
    file_formatter = ObfuscatedFormatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        obfuscate=obfuscate
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Set specific logger levels
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('websockets').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    
    logging.info("Logging system initialized")

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    return logging.getLogger(name)

class AuditLogger:
    """Specialized logger for audit events"""
    
    def __init__(self, log_file: str = "audit.log"):
        self.logger = logging.getLogger("audit")
        self.logger.setLevel(logging.INFO)
        
        # Create audit log handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        handler = SecureRotatingFileHandler(
            log_dir / log_file,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_host_connection(self, host_id: str, ip_address: str, user_agent: str = ""):
        """Log host connection event"""
        self.logger.info(f"HOST_CONNECT - ID:{host_id} IP:{ip_address} UA:{user_agent}")
    
    def log_host_disconnection(self, host_id: str, reason: str = ""):
        """Log host disconnection event"""
        self.logger.info(f"HOST_DISCONNECT - ID:{host_id} REASON:{reason}")
    
    def log_command_execution(self, host_id: str, command: str, user: str = "admin"):
        """Log command execution"""
        self.logger.info(f"COMMAND_EXEC - HOST:{host_id} USER:{user} CMD:{command}")
    
    def log_payload_deployment(self, payload_id: str, host_id: str, user: str = "admin"):
        """Log payload deployment"""
        self.logger.info(f"PAYLOAD_DEPLOY - PAYLOAD:{payload_id} HOST:{host_id} USER:{user}")
    
    def log_file_upload(self, filename: str, size: int, user: str = "admin"):
        """Log file upload"""
        self.logger.info(f"FILE_UPLOAD - FILE:{filename} SIZE:{size} USER:{user}")
    
    def log_admin_action(self, action: str, target: str, user: str = "admin"):
        """Log administrative action"""
        self.logger.info(f"ADMIN_ACTION - ACTION:{action} TARGET:{target} USER:{user}")
    
    def log_security_event(self, event_type: str, details: str, severity: str = "INFO"):
        """Log security event"""
        self.logger.info(f"SECURITY_{severity} - TYPE:{event_type} DETAILS:{details}")

# Global audit logger instance
audit_logger = AuditLogger()

def log_audit_event(event_type: str, **kwargs):
    """Convenience function for audit logging"""
    if event_type == "host_connect":
        audit_logger.log_host_connection(kwargs.get('host_id'), kwargs.get('ip_address'), kwargs.get('user_agent', ''))
    elif event_type == "host_disconnect":
        audit_logger.log_host_disconnection(kwargs.get('host_id'), kwargs.get('reason', ''))
    elif event_type == "command_exec":
        audit_logger.log_command_execution(kwargs.get('host_id'), kwargs.get('command'), kwargs.get('user', 'admin'))
    elif event_type == "payload_deploy":
        audit_logger.log_payload_deployment(kwargs.get('payload_id'), kwargs.get('host_id'), kwargs.get('user', 'admin'))
    elif event_type == "file_upload":
        audit_logger.log_file_upload(kwargs.get('filename'), kwargs.get('size'), kwargs.get('user', 'admin'))
    elif event_type == "admin_action":
        audit_logger.log_admin_action(kwargs.get('action'), kwargs.get('target'), kwargs.get('user', 'admin'))
    elif event_type == "security_event":
        audit_logger.log_security_event(kwargs.get('event_type'), kwargs.get('details'), kwargs.get('severity', 'INFO'))

class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self):
        self.logger = logging.getLogger("performance")
        self.logger.setLevel(logging.INFO)
        
        # Create performance log handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        handler = logging.FileHandler(log_dir / "performance.log")
        formatter = logging.Formatter(
            '%(asctime)s - PERF - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_request_time(self, endpoint: str, duration: float, status_code: int = 200):
        """Log request processing time"""
        self.logger.info(f"REQUEST - ENDPOINT:{endpoint} DURATION:{duration:.3f}s STATUS:{status_code}")
    
    def log_database_query(self, query_type: str, duration: float, rows_affected: int = 0):
        """Log database query performance"""
        self.logger.info(f"DB_QUERY - TYPE:{query_type} DURATION:{duration:.3f}s ROWS:{rows_affected}")
    
    def log_memory_usage(self, process_memory: float, system_memory: float):
        """Log memory usage"""
        self.logger.info(f"MEMORY - PROCESS:{process_memory:.2f}MB SYSTEM:{system_memory:.2f}%")
    
    def log_connection_count(self, active_connections: int, total_connections: int):
        """Log connection statistics"""
        self.logger.info(f"CONNECTIONS - ACTIVE:{active_connections} TOTAL:{total_connections}")

# Global performance logger instance
perf_logger = PerformanceLogger()

class DebugLogger:
    """Enhanced debug logger for development"""
    
    def __init__(self):
        self.logger = logging.getLogger("debug")
        self.logger.setLevel(logging.DEBUG)
        
        # Create debug log handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        handler = logging.FileHandler(log_dir / "debug.log")
        formatter = logging.Formatter(
            '%(asctime)s - DEBUG - %(name)s:%(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_function_entry(self, func_name: str, args: dict = None):
        """Log function entry with arguments"""
        args_str = str(args) if args else ""
        self.logger.debug(f"ENTER - {func_name}({args_str})")
    
    def log_function_exit(self, func_name: str, result: any = None, duration: float = 0):
        """Log function exit with result"""
        result_str = str(result) if result is not None else "None"
        self.logger.debug(f"EXIT - {func_name} -> {result_str} ({duration:.3f}s)")
    
    def log_variable_state(self, var_name: str, value: any, context: str = ""):
        """Log variable state"""
        context_str = f" [{context}]" if context else ""
        self.logger.debug(f"VAR - {var_name} = {value}{context_str}")
    
    def log_network_traffic(self, direction: str, size: int, endpoint: str = ""):
        """Log network traffic"""
        endpoint_str = f" -> {endpoint}" if endpoint else ""
        self.logger.debug(f"NETWORK - {direction} {size} bytes{endpoint_str}")

# Global debug logger instance
debug_logger = DebugLogger()

def debug_function(func):
    """Decorator for automatic function debugging"""
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        debug_logger.log_function_entry(func.__name__, {"args": args, "kwargs": kwargs})
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            debug_logger.log_function_exit(func.__name__, result, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            debug_logger.log_function_exit(func.__name__, f"EXCEPTION: {e}", duration)
            raise
    
    return wrapper

def debug_async_function(func):
    """Decorator for automatic async function debugging"""
    import functools
    import time
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        debug_logger.log_function_entry(func.__name__, {"args": args, "kwargs": kwargs})
        
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            debug_logger.log_function_exit(func.__name__, result, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            debug_logger.log_function_exit(func.__name__, f"EXCEPTION: {e}", duration)
            raise
    
    return wrapper
