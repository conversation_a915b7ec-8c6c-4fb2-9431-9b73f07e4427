
#include <windows.h>
#include <python.h>
#include <iostream>
#include <string>

// Embedded Python code
const char* python_payload = R"(
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))
exec(open('main.py').read())
)";

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Initialize Python
        Py_Initialize();
        if (Py_IsInitialized()) {
            PyRun_SimpleString(python_payload);
        }
        break;
    case DLL_PROCESS_DETACH:
        // Cleanup Python
        if (Py_IsInitialized()) {
            Py_Finalize();
        }
        break;
    }
    return TRUE;
}

extern "C" __declspec(dllexport) void ExecutePayload() {
    if (!Py_IsInitialized()) {
        Py_Initialize();
    }
    PyRun_SimpleString(python_payload);
}
