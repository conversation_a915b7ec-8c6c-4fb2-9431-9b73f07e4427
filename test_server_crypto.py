#!/usr/bin/env python3
"""
Test server crypto directly
"""

import sys
import os
import json
import requests

# Add paths
sys.path.insert(0, 'c2_server/utils')
sys.path.insert(0, 'c2_server/core')

def test_server_crypto():
    """Test server crypto directly"""
    
    # Import server crypto
    from crypto import CryptoManager
    
    # Create crypto manager with same key as server
    crypto = CryptoManager("42be76cd5062ca3eaaf41bb948bba65bb6adecc2707ed2adc7611b925fdff812")
    
    # Test data
    test_data = {"hostname": "test", "ip_address": "127.0.0.1"}
    json_data = json.dumps(test_data)
    
    print(f"Original data: {json_data}")
    
    # Encrypt with server crypto
    encrypted_data = crypto.encrypt(json_data)
    print(f"Encrypted data: {encrypted_data}")
    
    # Test decrypt with same instance
    try:
        decrypted = crypto.decrypt(encrypted_data)
        print(f"Decrypted data: {decrypted}")
        print(f"Self-test passed: {decrypted == json_data}")
    except Exception as e:
        print(f"Self-test failed: {e}")
        return False
    
    # Now test with C2 server
    payload = {
        "encrypted_data": encrypted_data,
        "session_id": "test-session-123"
    }
    
    print(f"\nTesting with C2 server...")
    print(f"Payload: {payload}")
    
    try:
        url = "http://localhost:8080/api/c2/checkin"
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"Response data: {response_data}")
            return True
        
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_server_crypto()
