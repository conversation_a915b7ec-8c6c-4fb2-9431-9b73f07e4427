#!/usr/bin/env python3
"""
Test script to verify import fixes
"""

import sys
import os

def test_c2_imports():
    """Test C2 server imports"""
    print("Testing C2 server imports...")
    try:
        sys.path.insert(0, 'c2_server')
        from main import main as c2_main
        print("✅ C2 server imports successful")
        return True
    except Exception as e:
        print(f"❌ C2 server import failed: {e}")
        return False
    finally:
        sys.path.remove('c2_server')

def test_loader_imports():
    """Test loader imports"""
    print("Testing loader imports...")
    try:
        sys.path.insert(0, 'loader')
        from main import main as loader_main
        print("✅ Loader imports successful")
        return True
    except Exception as e:
        print(f"❌ Loader import failed: {e}")
        return False
    finally:
        sys.path.remove('loader')

def test_payload_imports():
    """Test payload DLL imports"""
    print("Testing payload DLL imports...")
    try:
        sys.path.insert(0, 'payload_dll')
        from main import AdvancedPayload, dll_main
        print("✅ Payload DLL imports successful")
        return True
    except Exception as e:
        print(f"❌ Payload DLL import failed: {e}")
        return False
    finally:
        if 'payload_dll' in sys.path:
            sys.path.remove('payload_dll')

def main():
    """Main test function"""
    print("🧪 Testing import fixes...")
    print("=" * 50)
    
    results = []
    
    # Test each component
    results.append(test_c2_imports())
    results.append(test_loader_imports())
    results.append(test_payload_imports())
    
    print("=" * 50)
    
    if all(results):
        print("🎉 All imports working correctly!")
        return True
    else:
        print("⚠️  Some imports still have issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
