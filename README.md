# Educational C2 Server System

A comprehensive Command and Control (C2) server system built entirely in Python for educational purposes. This system demonstrates advanced C2 techniques including P2P networking, in-memory DLL execution, stealth operations, and persistence mechanisms.

## ⚠️ DISCLAIMER

**THIS SOFTWARE IS FOR EDUCATIONAL PURPOSES ONLY**

This project is designed for cybersecurity education, research, and authorized penetration testing. The author is not responsible for any misuse or damage caused by this software. Use only on systems you own or have explicit permission to test.

## 🎯 Features

### C2 Server
- **Real-time Dashboard**: Web-based interface for managing infected hosts
- **P2P Networking**: Distributed C2 infrastructure with peer-to-peer communication
- **Advanced Stealth**: Tor/I2P integration, domain fronting, traffic obfuscation
- **Payload Management**: Upload, manage, and deploy DLL payloads
- **Host Management**: Real-time host tracking, command execution, data exfiltration
- **Anti-Forensics**: Memory-only operations, log obfuscation, artifact cleanup

### Payload Loader
- **In-Memory Execution**: DLL loading and execution without touching disk
- **Stealth Techniques**: VM/sandbox detection, anti-debugging, junk code execution
- **Persistence**: Multiple persistence mechanisms across reboots and reinstalls
- **Self-Deletion**: Automatic cleanup after payload deployment

### DLL Payload Capabilities
- **Lateral Movement**: Network scanning and propagation
- **Social Engineering**: Email and social media account compromise
- **Crypto Mining**: XMrig integration with idle-time mining
- **Advanced Persistence**: Survives system reinstalls and reboots
- **C2 Communication**: Encrypted communication with command execution
- **Data Exfiltration**: File download/upload, screenshot capture, keylogging

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   C2 Server     │    │  Payload Loader │    │   DLL Payload   │
│                 │    │                 │    │                 │
│ • Dashboard     │◄──►│ • Downloads DLL │◄──►│ • Persistence   │
│ • P2P Network   │    │ • In-Memory Exec│    │ • Lateral Move  │
│ • Stealth Mgmt  │    │ • Self-Delete   │    │ • Crypto Mining │
│ • Host Tracking │    │ • Stealth Ops   │    │ • Social Eng    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Windows (for full DLL injection features) or Linux/macOS (limited features)
- Administrative privileges (recommended for full functionality)

### Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd educational-c2-system
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Launch the system**:
```bash
python launcher.py
```

### Alternative Launch Methods

**Server only**:
```bash
python launcher.py --server-only
```

**Test loader**:
```bash
python launcher.py --test-loader
```

**Setup only**:
```bash
python launcher.py --setup-only
```

## 📊 Dashboard Usage

1. **Access the dashboard**: Open `http://localhost:8080` in your browser
2. **View infected hosts**: Monitor connected hosts in real-time
3. **Execute commands**: Send commands to individual hosts or globally
4. **Manage payloads**: Upload DLL files and set active payloads
5. **Monitor logs**: View real-time system logs and events

### Dashboard Features
- **Host Management**: Click on hosts to select them for individual commands
- **Global Commands**: Send commands to all connected hosts simultaneously
- **Payload Upload**: Drag and drop DLL files for deployment
- **Real-time Updates**: WebSocket-based live updates
- **Statistics**: View connection counts, command history, and system status

## 🔧 Configuration

### C2 Server Configuration
Edit `c2_server/config.json`:
```json
{
    "SERVER_HOST": "0.0.0.0",
    "SERVER_PORT": 8443,
    "WEB_PORT": 8080,
    "P2P_ENABLED": true,
    "TOR_ENABLED": true,
    "STEALTH_MODE": true,
    "ANTI_FORENSICS": true
}
```

### Loader Configuration
Edit `loader/main.py`:
```python
self.c2_server = "127.0.0.1:8080"
self.encryption_key = "your_encryption_key"
```

### Payload Configuration
Edit `payload_dll/main.py`:
```python
self.wallet_address = "your_monero_wallet_address"
self.mining_intensity = 0.5  # 50% CPU when idle
```

## 🛡️ Security Features

### Encryption
- **AES-256 encryption** for all C2 communications
- **RSA key exchange** for secure session establishment
- **HMAC verification** for message integrity

### Stealth Techniques
- **VM/Sandbox detection** with evasion capabilities
- **Anti-debugging** measures
- **Process hollowing** and reflective DLL loading
- **Traffic obfuscation** and domain fronting
- **Junk code execution** to confuse analysis

### Persistence Mechanisms
- **Registry Run keys** (Windows)
- **Startup folder** placement
- **Scheduled tasks** creation
- **Windows services** (with admin privileges)
- **WMI event subscriptions** (advanced)

## 🌐 P2P Networking

The system supports peer-to-peer networking for distributed C2 operations:

- **Automatic peer discovery**
- **Data synchronization** across nodes
- **Command relaying** through the network
- **Tor/I2P integration** for anonymous communication
- **Fault tolerance** with automatic failover

## 📁 Project Structure

```
educational-c2-system/
├── c2_server/                 # C2 Server components
│   ├── core/                  # Core server modules
│   ├── utils/                 # Utility modules
│   ├── web/                   # Web dashboard
│   └── main.py               # Server entry point
├── loader/                    # Payload loader
│   ├── core/                  # Core loader modules
│   ├── utils/                 # Utility modules
│   └── main.py               # Loader entry point
├── payload_dll/               # DLL payload source
│   ├── core/                  # Payload modules
│   └── main.py               # Payload entry point
├── requirements.txt           # Python dependencies
├── launcher.py               # Unified launcher
└── README.md                 # This file
```

## 🧪 Testing

### Unit Tests
```bash
# Test C2 server components
cd c2_server
python -m pytest tests/

# Test loader components
cd loader
python -m pytest tests/
```

### Integration Tests
```bash
# Test full system integration
python launcher.py --test-loader
```

## 🔍 Educational Value

This project demonstrates:

1. **C2 Architecture**: Modern command and control design patterns
2. **Network Security**: P2P networking, encryption, and stealth techniques
3. **Malware Analysis**: Understanding advanced persistent threats (APTs)
4. **System Security**: Persistence mechanisms and evasion techniques
5. **Incident Response**: Recognizing and analyzing C2 communications

## 📚 Learning Resources

- **Code Comments**: Extensive documentation throughout the codebase
- **Modular Design**: Each component is self-contained for easy study
- **Real-world Techniques**: Implementation of actual APT techniques
- **Security Research**: Based on published research and CVEs

## 🤝 Contributing

This is an educational project. Contributions should focus on:
- Educational value and code clarity
- Security research and technique demonstration
- Documentation and learning resources
- Bug fixes and stability improvements

## 📄 License

This project is released under the MIT License for educational purposes. See LICENSE file for details.

## 🔗 References

- MITRE ATT&CK Framework
- Windows Internals documentation
- Malware analysis research papers
- Cybersecurity conference presentations

## 📞 Support

For educational questions and discussions:
- Review the code comments and documentation
- Check the issues section for common questions
- Ensure you understand the legal and ethical implications

---

**Remember**: This tool is for educational purposes only. Always obtain proper authorization before testing on any systems. The knowledge gained should be used to improve cybersecurity defenses, not to cause harm.
