#!/usr/bin/env python3
"""
Test crypto functionality
"""

import sys
import os
import json

# Add paths
sys.path.insert(0, 'loader/core')
sys.path.insert(0, 'c2_server/utils')

try:
    from crypto import CryptoManager as LoaderCrypto
except ImportError:
    print("Failed to import loader crypto")
    sys.exit(1)

try:
    sys.path.insert(0, 'c2_server/utils')
    from crypto import CryptoManager as ServerCrypto
except ImportError:
    print("Failed to import server crypto")
    sys.exit(1)

def test_crypto():
    """Test crypto compatibility"""
    key = "default_key_change_me"
    
    # Create crypto managers
    loader_crypto = LoaderCrypto(key)
    server_crypto = ServerCrypto(key)
    
    # Test data
    test_data = {"test": "data", "number": 123}
    json_data = json.dumps(test_data)
    
    print(f"Original data: {json_data}")
    
    # Encrypt with loader
    encrypted = loader_crypto.encrypt(json_data)
    print(f"Encrypted: {encrypted}")
    
    # Decrypt with server
    try:
        decrypted = server_crypto.decrypt(encrypted)
        print(f"Decrypted: {decrypted}")
        
        if decrypted == json_data:
            print("✅ Crypto compatibility test PASSED")
            return True
        else:
            print("❌ Crypto compatibility test FAILED - data mismatch")
            return False
    except Exception as e:
        print(f"❌ Crypto compatibility test FAILED - {e}")
        return False

if __name__ == "__main__":
    test_crypto()
