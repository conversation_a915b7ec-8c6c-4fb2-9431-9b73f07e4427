
# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['C:\Users\<USER>\OneDrive\Desktop\worm\builder\payload_dll\main.py'],
    pathex=['C:\Users\<USER>\OneDrive\Desktop\worm\builder\payload_dll'],
    binaries=[],
    datas=[],
    hiddenimports=['cryptography', 'requests', 'psutil'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='advanced_payload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt'
)
