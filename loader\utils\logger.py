"""
Logging utilities for Payload Loader
Provides minimal and stealthy logging capabilities
"""

import logging
import os
import sys
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Optional

class StealthFormatter(logging.Formatter):
    """Minimal formatter for stealth logging"""
    
    def format(self, record):
        """Format log record minimally"""
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        return f"[{timestamp}] {record.levelname[0]}: {record.getMessage()}"

class MemoryHandler(logging.Handler):
    """In-memory log handler that doesn't write to disk"""
    
    def __init__(self, maxlen=100):
        super().__init__()
        self.logs = []
        self.maxlen = maxlen
    
    def emit(self, record):
        """Store log record in memory"""
        try:
            msg = self.format(record)
            self.logs.append(msg)
            
            # Keep only the last maxlen entries
            if len(self.logs) > self.maxlen:
                self.logs.pop(0)
        except Exception:
            pass  # Silently ignore logging errors
    
    def get_logs(self):
        """Get all stored logs"""
        return self.logs.copy()
    
    def clear(self):
        """Clear all stored logs"""
        self.logs.clear()

class NullHandler(logging.Handler):
    """Handler that discards all log records"""
    
    def emit(self, record):
        """Discard the log record"""
        pass

# Global memory handler for accessing logs
_memory_handler = None

def setup_logging(log_level: str = "WARNING", log_to_file: bool = False, 
                 stealth_mode: bool = True):
    """Setup logging configuration for the loader"""
    global _memory_handler
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Setup formatter
    formatter = StealthFormatter()
    
    if stealth_mode:
        # In stealth mode, use memory handler only
        _memory_handler = MemoryHandler(maxlen=50)
        _memory_handler.setFormatter(formatter)
        root_logger.addHandler(_memory_handler)
        
        # Also add null handler to suppress any other logging
        null_handler = NullHandler()
        root_logger.addHandler(null_handler)
        
    else:
        # Non-stealth mode: allow console and file logging
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # Memory handler for log access
        _memory_handler = MemoryHandler(maxlen=100)
        _memory_handler.setFormatter(formatter)
        root_logger.addHandler(_memory_handler)
        
        # File handler if requested
        if log_to_file:
            try:
                # Use temp directory for log file
                log_dir = tempfile.gettempdir()
                log_file = os.path.join(log_dir, f"loader_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
                
                file_handler = logging.FileHandler(log_file)
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)
                
            except Exception:
                pass  # Silently ignore file logging errors
    
    # Suppress noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('requests').setLevel(logging.ERROR)
    logging.getLogger('cryptography').setLevel(logging.ERROR)

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    return logging.getLogger(name)

def get_memory_logs() -> list:
    """Get logs stored in memory"""
    global _memory_handler
    if _memory_handler:
        return _memory_handler.get_logs()
    return []

def clear_memory_logs():
    """Clear logs stored in memory"""
    global _memory_handler
    if _memory_handler:
        _memory_handler.clear()

class QuietLogger:
    """Ultra-minimal logger that only stores critical errors"""
    
    def __init__(self):
        self.errors = []
        self.max_errors = 10
    
    def error(self, msg: str):
        """Log an error message"""
        self.errors.append(f"ERROR: {msg}")
        if len(self.errors) > self.max_errors:
            self.errors.pop(0)
    
    def warning(self, msg: str):
        """Log a warning (ignored in quiet mode)"""
        pass
    
    def info(self, msg: str):
        """Log info (ignored in quiet mode)"""
        pass
    
    def debug(self, msg: str):
        """Log debug (ignored in quiet mode)"""
        pass
    
    def get_errors(self) -> list:
        """Get stored errors"""
        return self.errors.copy()
    
    def clear_errors(self):
        """Clear stored errors"""
        self.errors.clear()

# Global quiet logger instance
quiet_logger = QuietLogger()

def get_quiet_logger() -> QuietLogger:
    """Get the quiet logger instance"""
    return quiet_logger
