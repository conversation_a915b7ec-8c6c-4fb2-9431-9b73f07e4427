"""
I2P Proxy Integration for C2 Server
Provides I2P network connectivity for anonymous communications
"""

import asyncio
import socket
import socks
from typing import Optional, Dict, Any
import aiohttp
import aiohttp_socks

from .logger import get_logger

class I2PProxy:
    """I2P proxy manager for anonymous communications"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Parse I2P proxy configuration
        proxy_parts = config.I2P_PROXY.split(':')
        self.i2p_host = proxy_parts[0]
        self.i2p_port = int(proxy_parts[1])
        
        # Connection state
        self.connected = False
        self.session = None
        
        # I2P configuration
        self.sam_port = 7656  # SAM (Simple Anonymous Messaging) port
        self.tunnel_name = "c2_tunnel"
        
    async def connect(self) -> bool:
        """Connect to I2P proxy"""
        try:
            # Test I2P connectivity
            if await self._test_i2p_connection():
                self.connected = True
                self.logger.info(f"Connected to I2P proxy at {self.i2p_host}:{self.i2p_port}")
                
                # Create aiohttp session with I2P proxy
                connector = aiohttp_socks.ProxyConnector.from_url(
                    f'socks5://{self.i2p_host}:{self.i2p_port}'
                )
                self.session = aiohttp.ClientSession(connector=connector)
                
                return True
            else:
                self.logger.error("Failed to connect to I2P proxy")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to I2P: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from I2P proxy"""
        if self.session:
            await self.session.close()
            self.session = None
        
        self.connected = False
        self.logger.info("Disconnected from I2P proxy")
    
    async def _test_i2p_connection(self) -> bool:
        """Test if I2P proxy is accessible"""
        try:
            # Create a socket and connect through SOCKS5
            sock = socks.socksocket()
            sock.set_proxy(socks.SOCKS5, self.i2p_host, self.i2p_port)
            sock.settimeout(10)
            
            # Try to connect to an I2P eepsite (would need actual I2P site)
            # For now, just test the proxy connection
            sock.connect(("127.0.0.1", 80))
            sock.close()
            
            return True
            
        except Exception as e:
            self.logger.debug(f"I2P connection test: {e}")
            # I2P might not have accessible clearnet, so we'll assume it's working
            # if the proxy port is open
            return await self._test_proxy_port()
    
    async def _test_proxy_port(self) -> bool:
        """Test if I2P proxy port is open"""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(self.i2p_host, self.i2p_port),
                timeout=5
            )
            writer.close()
            await writer.wait_closed()
            return True
        except Exception:
            return False
    
    async def make_request(self, method: str, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """Make HTTP request through I2P"""
        if not self.session:
            self.logger.error("I2P session not available")
            return None
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                return response
        except Exception as e:
            self.logger.error(f"I2P request failed: {e}")
            return None
    
    async def get(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """GET request through I2P"""
        return await self.make_request('GET', url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """POST request through I2P"""
        return await self.make_request('POST', url, **kwargs)
    
    def create_i2p_socket(self) -> socket.socket:
        """Create a socket configured for I2P"""
        sock = socks.socksocket()
        sock.set_proxy(socks.SOCKS5, self.i2p_host, self.i2p_port)
        return sock
    
    async def create_sam_session(self, session_name: str = None) -> bool:
        """Create SAM session for I2P communication"""
        if not session_name:
            session_name = self.tunnel_name
        
        try:
            reader, writer = await asyncio.open_connection(
                self.i2p_host, self.sam_port
            )
            
            # SAM handshake
            writer.write(b'HELLO VERSION MIN=3.0 MAX=3.3\n')
            await writer.drain()
            response = await reader.readline()
            
            if b'HELLO REPLY RESULT=OK' not in response:
                self.logger.error("SAM handshake failed")
                return False
            
            # Create session
            session_cmd = f'SESSION CREATE STYLE=STREAM ID={session_name} DESTINATION=TRANSIENT\n'
            writer.write(session_cmd.encode())
            await writer.drain()
            response = await reader.readline()
            
            writer.close()
            await writer.wait_closed()
            
            if b'SESSION STATUS RESULT=OK' in response:
                self.logger.info(f"Created SAM session: {session_name}")
                return True
            else:
                self.logger.error(f"Failed to create SAM session: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error creating SAM session: {e}")
            return False
    
    async def get_destination(self, session_name: str = None) -> Optional[str]:
        """Get I2P destination for the session"""
        if not session_name:
            session_name = self.tunnel_name
        
        try:
            reader, writer = await asyncio.open_connection(
                self.i2p_host, self.sam_port
            )
            
            # SAM handshake
            writer.write(b'HELLO VERSION MIN=3.0 MAX=3.3\n')
            await writer.drain()
            response = await reader.readline()
            
            if b'HELLO REPLY RESULT=OK' not in response:
                return None
            
            # Get destination
            dest_cmd = f'DEST GENERATE\n'
            writer.write(dest_cmd.encode())
            await writer.drain()
            response = await reader.readline()
            
            writer.close()
            await writer.wait_closed()
            
            if b'DEST REPLY' in response:
                # Parse destination from response
                response_str = response.decode()
                if 'PUB=' in response_str:
                    destination = response_str.split('PUB=')[1].split(' ')[0]
                    self.logger.info(f"Generated I2P destination: {destination[:50]}...")
                    return destination
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting I2P destination: {e}")
            return None
    
    async def connect_to_destination(self, destination: str, session_name: str = None) -> bool:
        """Connect to an I2P destination"""
        if not session_name:
            session_name = self.tunnel_name
        
        try:
            reader, writer = await asyncio.open_connection(
                self.i2p_host, self.sam_port
            )
            
            # SAM handshake
            writer.write(b'HELLO VERSION MIN=3.0 MAX=3.3\n')
            await writer.drain()
            response = await reader.readline()
            
            if b'HELLO REPLY RESULT=OK' not in response:
                return False
            
            # Connect to destination
            connect_cmd = f'STREAM CONNECT ID={session_name} DESTINATION={destination}\n'
            writer.write(connect_cmd.encode())
            await writer.drain()
            response = await reader.readline()
            
            if b'STREAM STATUS RESULT=OK' in response:
                self.logger.info(f"Connected to I2P destination")
                # Keep connection open for communication
                return True
            else:
                self.logger.error(f"Failed to connect to destination: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to I2P destination: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Check if connected to I2P"""
        return self.connected
    
    async def check_i2p_status(self) -> Dict[str, Any]:
        """Check I2P daemon status"""
        status = {
            "connected": self.connected,
            "proxy_host": self.i2p_host,
            "proxy_port": self.i2p_port,
            "sam_port": self.sam_port,
            "tunnels": []
        }
        
        if self.connected:
            # Would check tunnel status here
            pass
        
        return status
    
    async def download_file_through_i2p(self, url: str, output_path: str) -> bool:
        """Download file through I2P"""
        if not self.session:
            return False
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    
                    self.logger.info(f"Downloaded file through I2P: {output_path}")
                    return True
                else:
                    self.logger.error(f"Download failed with status: {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error downloading file through I2P: {e}")
            return False
    
    async def upload_file_through_i2p(self, url: str, file_path: str, 
                                    field_name: str = "file") -> bool:
        """Upload file through I2P"""
        if not self.session:
            return False
        
        try:
            with open(file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field(field_name, f, filename=file_path)
                
                async with self.session.post(url, data=data) as response:
                    if response.status == 200:
                        self.logger.info(f"Uploaded file through I2P: {file_path}")
                        return True
                    else:
                        self.logger.error(f"Upload failed with status: {response.status}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Error uploading file through I2P: {e}")
            return False
    
    def get_proxy_settings(self) -> Dict[str, str]:
        """Get proxy settings for external tools"""
        return {
            "http_proxy": f"socks5://{self.i2p_host}:{self.i2p_port}",
            "https_proxy": f"socks5://{self.i2p_host}:{self.i2p_port}",
            "socks_proxy": f"socks5://{self.i2p_host}:{self.i2p_port}"
        }
    
    async def test_eepsite(self, eepsite_address: str) -> bool:
        """Test connectivity to an eepsite"""
        if not self.session:
            return False
        
        try:
            async with self.session.get(f"http://{eepsite_address}", timeout=60) as response:
                return response.status == 200
        except Exception as e:
            self.logger.error(f"Eepsite test failed: {e}")
            return False
    
    async def create_tunnel(self, local_port: int, destination: str = None) -> Optional[str]:
        """Create an I2P tunnel"""
        try:
            # This would involve configuring I2P tunnels
            # Implementation depends on I2P router configuration
            self.logger.info(f"Creating I2P tunnel on port {local_port}")
            
            # Would create actual tunnel configuration here
            # For now, return a placeholder destination
            if not destination:
                destination = await self.get_destination()
            
            return destination
            
        except Exception as e:
            self.logger.error(f"Error creating I2P tunnel: {e}")
            return None
    
    async def list_tunnels(self) -> Dict[str, Any]:
        """List active I2P tunnels"""
        try:
            # Would query I2P router for tunnel information
            tunnels = {
                "client_tunnels": [],
                "server_tunnels": []
            }
            
            return tunnels
            
        except Exception as e:
            self.logger.error(f"Error listing I2P tunnels: {e}")
            return {}
    
    async def get_router_info(self) -> Dict[str, Any]:
        """Get I2P router information"""
        try:
            # Would query I2P router for status information
            router_info = {
                "version": "unknown",
                "uptime": 0,
                "peers": 0,
                "tunnels": 0,
                "status": "unknown"
            }
            
            return router_info
            
        except Exception as e:
            self.logger.error(f"Error getting I2P router info: {e}")
            return {}
    
    async def reseed_router(self) -> bool:
        """Trigger I2P router reseed"""
        try:
            # Would trigger router reseed operation
            self.logger.info("Triggering I2P router reseed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error reseeding I2P router: {e}")
            return False
