"""
Tor Proxy Integration for C2 Server
Provides Tor network connectivity for anonymous communications
"""

import asyncio
import socket
import socks
import requests
from typing import Optional, Dict, Any
import aiohttp
import aiohttp_socks

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from logger import get_logger

class TorProxy:
    """Tor proxy manager for anonymous communications"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Parse Tor proxy configuration
        proxy_parts = config.TOR_PROXY.split(':')
        self.tor_host = proxy_parts[0]
        self.tor_port = int(proxy_parts[1])
        
        # Connection state
        self.connected = False
        self.session = None
        
        # Tor configuration
        self.control_port = 9051
        self.control_password = None
        
    async def connect(self) -> bool:
        """Connect to Tor proxy"""
        try:
            # Test Tor connectivity
            if await self._test_tor_connection():
                self.connected = True
                self.logger.info(f"Connected to Tor proxy at {self.tor_host}:{self.tor_port}")
                
                # Create aiohttp session with Tor proxy
                connector = aiohttp_socks.ProxyConnector.from_url(
                    f'socks5://{self.tor_host}:{self.tor_port}'
                )
                self.session = aiohttp.ClientSession(connector=connector)
                
                # Get current Tor IP
                current_ip = await self.get_current_ip()
                if current_ip:
                    self.logger.info(f"Tor exit IP: {current_ip}")
                
                return True
            else:
                self.logger.error("Failed to connect to Tor proxy")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to Tor: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Tor proxy"""
        if self.session:
            await self.session.close()
            self.session = None
        
        self.connected = False
        self.logger.info("Disconnected from Tor proxy")
    
    async def _test_tor_connection(self) -> bool:
        """Test if Tor proxy is accessible"""
        try:
            # Create a socket and connect through SOCKS5
            sock = socks.socksocket()
            sock.set_proxy(socks.SOCKS5, self.tor_host, self.tor_port)
            sock.settimeout(10)
            
            # Try to connect to a test server
            sock.connect(("httpbin.org", 80))
            sock.close()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Tor connection test failed: {e}")
            return False
    
    async def get_current_ip(self) -> Optional[str]:
        """Get current exit IP through Tor"""
        if not self.session:
            return None
        
        try:
            async with self.session.get('http://httpbin.org/ip', timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('origin')
        except Exception as e:
            self.logger.error(f"Failed to get Tor IP: {e}")
        
        return None
    
    async def make_request(self, method: str, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """Make HTTP request through Tor"""
        if not self.session:
            self.logger.error("Tor session not available")
            return None
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                return response
        except Exception as e:
            self.logger.error(f"Tor request failed: {e}")
            return None
    
    async def get(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """GET request through Tor"""
        return await self.make_request('GET', url, **kwargs)
    
    async def post(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """POST request through Tor"""
        return await self.make_request('POST', url, **kwargs)
    
    def create_tor_socket(self) -> socket.socket:
        """Create a socket configured for Tor"""
        sock = socks.socksocket()
        sock.set_proxy(socks.SOCKS5, self.tor_host, self.tor_port)
        return sock
    
    async def new_identity(self) -> bool:
        """Request new Tor identity (new circuit)"""
        try:
            # Connect to Tor control port
            reader, writer = await asyncio.open_connection(
                self.tor_host, self.control_port
            )
            
            # Authenticate if password is set
            if self.control_password:
                writer.write(f'AUTHENTICATE "{self.control_password}"\r\n'.encode())
            else:
                writer.write(b'AUTHENTICATE\r\n')
            
            await writer.drain()
            response = await reader.readline()
            
            if b'250 OK' not in response:
                self.logger.error("Tor authentication failed")
                return False
            
            # Send NEWNYM command for new identity
            writer.write(b'SIGNAL NEWNYM\r\n')
            await writer.drain()
            response = await reader.readline()
            
            writer.close()
            await writer.wait_closed()
            
            if b'250 OK' in response:
                self.logger.info("New Tor identity requested")
                
                # Wait a bit for circuit to establish
                await asyncio.sleep(5)
                
                # Verify new IP
                new_ip = await self.get_current_ip()
                if new_ip:
                    self.logger.info(f"New Tor exit IP: {new_ip}")
                
                return True
            else:
                self.logger.error("Failed to request new Tor identity")
                return False
                
        except Exception as e:
            self.logger.error(f"Error requesting new Tor identity: {e}")
            return False
    
    async def get_circuit_info(self) -> Dict[str, Any]:
        """Get information about current Tor circuits"""
        try:
            reader, writer = await asyncio.open_connection(
                self.tor_host, self.control_port
            )
            
            # Authenticate
            if self.control_password:
                writer.write(f'AUTHENTICATE "{self.control_password}"\r\n'.encode())
            else:
                writer.write(b'AUTHENTICATE\r\n')
            
            await writer.drain()
            response = await reader.readline()
            
            if b'250 OK' not in response:
                return {}
            
            # Get circuit information
            writer.write(b'GETINFO circuit-status\r\n')
            await writer.drain()
            
            circuits = []
            while True:
                line = await reader.readline()
                if line.startswith(b'250 OK'):
                    break
                elif line.startswith(b'250-circuit-status='):
                    circuit_data = line.decode().strip()
                    circuits.append(circuit_data)
            
            writer.close()
            await writer.wait_closed()
            
            return {"circuits": circuits}
            
        except Exception as e:
            self.logger.error(f"Error getting circuit info: {e}")
            return {}
    
    def is_connected(self) -> bool:
        """Check if connected to Tor"""
        return self.connected
    
    async def check_tor_status(self) -> Dict[str, Any]:
        """Check Tor daemon status"""
        status = {
            "connected": self.connected,
            "proxy_host": self.tor_host,
            "proxy_port": self.tor_port,
            "exit_ip": None,
            "circuits": 0
        }
        
        if self.connected:
            status["exit_ip"] = await self.get_current_ip()
            circuit_info = await self.get_circuit_info()
            status["circuits"] = len(circuit_info.get("circuits", []))
        
        return status
    
    async def download_file_through_tor(self, url: str, output_path: str) -> bool:
        """Download file through Tor"""
        if not self.session:
            return False
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    
                    self.logger.info(f"Downloaded file through Tor: {output_path}")
                    return True
                else:
                    self.logger.error(f"Download failed with status: {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error downloading file through Tor: {e}")
            return False
    
    async def upload_file_through_tor(self, url: str, file_path: str, 
                                    field_name: str = "file") -> bool:
        """Upload file through Tor"""
        if not self.session:
            return False
        
        try:
            with open(file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field(field_name, f, filename=file_path)
                
                async with self.session.post(url, data=data) as response:
                    if response.status == 200:
                        self.logger.info(f"Uploaded file through Tor: {file_path}")
                        return True
                    else:
                        self.logger.error(f"Upload failed with status: {response.status}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Error uploading file through Tor: {e}")
            return False
    
    def get_proxy_settings(self) -> Dict[str, str]:
        """Get proxy settings for external tools"""
        return {
            "http_proxy": f"socks5://{self.tor_host}:{self.tor_port}",
            "https_proxy": f"socks5://{self.tor_host}:{self.tor_port}",
            "socks_proxy": f"socks5://{self.tor_host}:{self.tor_port}"
        }
    
    async def test_hidden_service(self, onion_address: str) -> bool:
        """Test connectivity to a hidden service"""
        if not self.session:
            return False
        
        try:
            async with self.session.get(f"http://{onion_address}", timeout=30) as response:
                return response.status == 200
        except Exception as e:
            self.logger.error(f"Hidden service test failed: {e}")
            return False
    
    async def create_hidden_service(self, local_port: int, 
                                  service_port: int = 80) -> Optional[str]:
        """Create a Tor hidden service"""
        try:
            reader, writer = await asyncio.open_connection(
                self.tor_host, self.control_port
            )
            
            # Authenticate
            if self.control_password:
                writer.write(f'AUTHENTICATE "{self.control_password}"\r\n'.encode())
            else:
                writer.write(b'AUTHENTICATE\r\n')
            
            await writer.drain()
            response = await reader.readline()
            
            if b'250 OK' not in response:
                return None
            
            # Add hidden service
            writer.write(f'ADD_ONION NEW:BEST Port={service_port},127.0.0.1:{local_port}\r\n'.encode())
            await writer.drain()
            
            response = await reader.readline()
            response_str = response.decode().strip()
            
            writer.close()
            await writer.wait_closed()
            
            if response_str.startswith('250-ServiceID='):
                onion_address = response_str.split('=')[1] + '.onion'
                self.logger.info(f"Created hidden service: {onion_address}")
                return onion_address
            else:
                self.logger.error(f"Failed to create hidden service: {response_str}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating hidden service: {e}")
            return None
