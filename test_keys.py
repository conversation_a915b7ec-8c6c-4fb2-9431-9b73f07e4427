#!/usr/bin/env python3
"""
Test if both crypto managers generate the same keys
"""

import sys
import os
import base64

# Add paths
sys.path.insert(0, 'loader/core')
sys.path.insert(0, 'c2_server/utils')

from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

def derive_fernet_key_loader(master_key):
    """Derive key using loader method"""
    master_key = master_key.encode() if isinstance(master_key, str) else master_key
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=b'loader_salt_key',
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(master_key))
    return key

def derive_fernet_key_server(master_key):
    """Derive key using server method"""
    master_key = master_key.encode() if isinstance(master_key, str) else master_key
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=b'loader_salt_key',  # Should match loader
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(master_key))
    return key

def test_key_derivation():
    """Test key derivation"""
    master_key = "default_key_change_me"
    
    loader_key = derive_fernet_key_loader(master_key)
    server_key = derive_fernet_key_server(master_key)
    
    print(f"Master key: {master_key}")
    print(f"Loader key: {loader_key}")
    print(f"Server key: {server_key}")
    print(f"Keys match: {loader_key == server_key}")
    
    return loader_key == server_key

if __name__ == "__main__":
    test_key_derivation()
