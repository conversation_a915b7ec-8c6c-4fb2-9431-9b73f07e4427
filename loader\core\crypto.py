"""
Cryptographic utilities for Payload Loader
Provides encryption, decryption, and secure communication functions
"""

import base64
import hashlib
import hmac
import os
import secrets
from typing import Union, Tuple, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class CryptoManager:
    """Manages cryptographic operations for the payload loader"""
    
    def __init__(self, master_key: str):
        self.master_key = master_key.encode() if isinstance(master_key, str) else master_key
        self.fernet = Fernet(self._derive_fernet_key())
    
    def _derive_fernet_key(self) -> bytes:
        """Derive a Fernet key from the master key"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'loader_salt_key',  # Should match C2 server
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key))
        return key
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """Encrypt data using Fernet symmetric encryption"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        encrypted = self.fernet.encrypt(data)
        return base64.b64encode(encrypted).decode('utf-8')
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt data using Fernet symmetric encryption"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode('utf-8')
        except Exception as e:
            raise ValueError(f"Decryption failed: {e}")
    
    def encrypt_bytes(self, data: bytes) -> bytes:
        """Encrypt raw bytes"""
        return self.fernet.encrypt(data)
    
    def decrypt_bytes(self, encrypted_data: bytes) -> bytes:
        """Decrypt raw bytes"""
        return self.fernet.decrypt(encrypted_data)
    
    def generate_hmac(self, data: Union[str, bytes], key: bytes = None) -> str:
        """Generate HMAC for data integrity"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        if key is None:
            key = self.master_key
        
        mac = hmac.new(key, data, hashlib.sha256)
        return base64.b64encode(mac.digest()).decode()
    
    def verify_hmac(self, data: Union[str, bytes], mac: str, key: bytes = None) -> bool:
        """Verify HMAC"""
        expected_mac = self.generate_hmac(data, key)
        return hmac.compare_digest(mac, expected_mac)
    
    def hash_data(self, data: Union[str, bytes]) -> str:
        """Generate SHA256 hash of data"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        return hashlib.sha256(data).hexdigest()
    
    def secure_random_bytes(self, length: int) -> bytes:
        """Generate secure random bytes"""
        return secrets.token_bytes(length)
    
    def secure_random_string(self, length: int) -> str:
        """Generate secure random string"""
        alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def xor_encrypt(self, data: bytes, key: bytes) -> bytes:
        """Simple XOR encryption for obfuscation"""
        result = bytearray()
        key_len = len(key)
        
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        
        return bytes(result)
    
    def xor_decrypt(self, data: bytes, key: bytes) -> bytes:
        """Simple XOR decryption"""
        return self.xor_encrypt(data, key)  # XOR is its own inverse
